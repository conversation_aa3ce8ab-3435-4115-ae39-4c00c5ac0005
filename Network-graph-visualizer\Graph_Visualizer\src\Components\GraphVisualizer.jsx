import { useEffect, useRef, useState } from "react";
import * as d3 from "d3";

const GraphVisualizer = ({nodesData, linksData, isDirected, isWeighted}) => {
    const svgRef = useRef();
    const [selectedNode, setSelectedNode] = useState(null);
    const [hoveredNode, setHoveredNode] = useState(null);
    const [zoomLevel, setZoomLevel] = useState(1);

    useEffect(() => {
        const width = 1000;
        const height = 600;
        const radius = window.innerWidth < 768 ? 20 : 12;
        const isMobile = window.innerWidth < 768;
        const fontSize = isMobile ? "16px" : "14px";

        const svg = d3
            .select(svgRef.current)
            .attr("width", "100%")
            .attr("height", "100%")
            .attr("viewBox", `0 0 ${width} ${height}`)
            .attr("preserveAspectRatio", "xMidYMid meet")
            .style("background", "white")
            .style("border-radius", "8px");

        svg.selectAll("*").remove(); // Clear before re-render

        // Add zoom and pan functionality
        const zoom = d3.zoom()
            .scaleExtent([0.1, 4])
            .on("zoom", (event) => {
                container.attr("transform", event.transform);
                setZoomLevel(event.transform.k);
            });

        svg.call(zoom);

        // Create main container for all graph elements
        const container = svg.append("g").attr("class", "graph-container");

        // Add arrow marker (used conditionally)
        const defs = svg.append("defs");

        defs.append("marker")
            .attr("id", "arrow")
            .attr("viewBox", "0 -5 10 10")
            .attr("refx", 20)
            .attr("refY", 0)
            .attr("markerWidth", 6)
            .attr("markerHeight", 6)
            .attr("orient", "auto")
            .append("path")
            .attr("d", "M0,-5L10,0L0,5")
            .attr("fill", "#999");

        // Add glow filter for highlighted elements
        const filter = defs.append("filter")
            .attr("id", "glow");

        filter.append("feGaussianBlur")
            .attr("stdDeviation", "3")
            .attr("result", "coloredBlur");

        const feMerge = filter.append("feMerge");
        feMerge.append("feMergeNode").attr("in", "coloredBlur");
        feMerge.append("feMergeNode").attr("in", "SourceGraphic");
        
        const simulation = d3
            .forceSimulation(nodesData)
            .force("link", d3.forceLink(linksData).id(d => d.id).distance(80).strength(0.8))
            .force("charge", d3.forceManyBody().strength(-400))
            .force("center", d3.forceCenter(width / 2, height / 2))
            .force("collision", d3.forceCollide().radius(radius + 5));

        // Create links
        const link = container
            .append("g")
            .attr("class", "links")
            .selectAll("line")
            .data(linksData)
            .enter()
            .append("line")
            .attr("stroke", d => d.color || "#6b7280")
            .attr("stroke-width", d => d.highlighted ? 4 : 2.5)
            .attr("marker-end", isDirected ? "url(#arrow)" : null)
            .attr("opacity", 0.8)
            .style("transition", "all 0.3s ease");

        // Create nodes
        const node = container
            .append("g")
            .attr("class", "nodes")
            .selectAll("circle")
            .data(nodesData)
            .enter()
            .append("circle")
            .attr("r", d => d.size || radius)
            .attr("fill", d => d.color || "#3b82f6")
            .attr("stroke", d => d.id === selectedNode ? "#1f2937" : "#ffffff")
            .attr("stroke-width", d => d.id === selectedNode ? 3 : 2)
            .attr("filter", d => d.highlighted ? "url(#glow)" : null)
            .style("cursor", "pointer")
            .style("transition", "all 0.3s ease")
            .on("mouseover", function(_, d) {
                setHoveredNode(d.id);
                d3.select(this)
                    .transition()
                    .duration(200)
                    .attr("r", (d.size || radius) * 1.2)
                    .attr("stroke-width", 3);
            })
            .on("mouseout", function(_, d) {
                setHoveredNode(null);
                d3.select(this)
                    .transition()
                    .duration(200)
                    .attr("r", d.size || radius)
                    .attr("stroke-width", d.id === selectedNode ? 3 : 2);
            })
            .on("click", function(_, d) {
                setSelectedNode(d.id === selectedNode ? null : d.id);
            })
            .call(d3.drag()
                .on("start", dragstarted)
                .on("drag", dragged)
                .on("end", dragended));

        // Add node labels
        const labels = container
            .append("g")
            .attr("class", "labels")
            .selectAll("text")
            .data(nodesData)
            .enter()
            .append("text")
            .text(d => d.label || d.id)
            .attr("font-size", fontSize)
            .attr("font-family", "Arial, sans-serif")
            .attr("fill", "#333")
            .attr("text-anchor", "middle")
            .attr("dy", ".35em")
            .style("pointer-events", "none")
            .style("user-select", "none");

        // Add edge labels for weighted graphs
        let edgeLabels;
        if (isWeighted) {
            edgeLabels = container
                .append("g")
                .attr("class", "edge-labels")
                .selectAll("text")
                .data(linksData)
                .enter()
                .append("text")
                .text(d => d.weight)
                .attr("font-size", "10px")
                .attr("font-family", "Arial, sans-serif")
                .attr("fill", "#666")
                .attr("text-anchor", "middle")
                .style("pointer-events", "none")
                .style("user-select", "none");
        }

        // Update positions on simulation tick
        simulation.on("tick", () => {
            link
                .attr("x1", d => d.source.x)
                .attr("y1", d => d.source.y)
                .attr("x2", d => d.target.x)
                .attr("y2", d => d.target.y);

            node
                .attr("cx", d => d.x)
                .attr("cy", d => d.y);

            labels
                .attr("x", d => d.x)
                .attr("y", d => d.y);

            if (isWeighted && edgeLabels) {
                edgeLabels
                    .attr("x", d => (d.source.x + d.target.x) / 2)
                    .attr("y", d => (d.source.y + d.target.y) / 2);
            }
        });

        // Drag functions
        function dragstarted(event, d) {
            if (!event.active) simulation.alphaTarget(0.3).restart();
            d.fx = d.x;
            d.fy = d.y;
        }

        function dragged(event, d) {
            d.fx = event.x;
            d.fy = event.y;
        }

        function dragended(event, d) {
            if (!event.active) simulation.alphaTarget(0);
            d.fx = null;
            d.fy = null;
        }

    }, [nodesData, linksData, isDirected, isWeighted]);

    const handleResetZoom = () => {
        const svg = d3.select(svgRef.current);
        svg.transition().duration(750).call(
            d3.zoom().transform,
            d3.zoomIdentity
        );
        setZoomLevel(1);
    };

    const handleFitToScreen = () => {
        const svg = d3.select(svgRef.current);
        const bounds = svg.select(".graph-container").node()?.getBBox();
        if (bounds) {
            const width = 800;
            const height = 800;
            const scale = Math.min(width / bounds.width, height / bounds.height) * 0.9;
            const translate = [
                width / 2 - scale * (bounds.x + bounds.width / 2),
                height / 2 - scale * (bounds.y + bounds.height / 2)
            ];

            svg.transition().duration(750).call(
                d3.zoom().transform,
                d3.zoomIdentity.translate(translate[0], translate[1]).scale(scale)
            );
            setZoomLevel(scale);
        }
    };

    return (
        <div className="w-full h-full relative bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700">
            <svg ref={svgRef} className="w-full h-full min-h-[500px]"></svg>

            {/* Zoom Controls */}
            <div className="absolute top-4 right-4 flex flex-col gap-2">
                <button
                    onClick={handleResetZoom}
                    className="p-2 bg-white dark:bg-gray-700 rounded-lg shadow-md hover:shadow-lg transition-shadow"
                    title="Reset Zoom"
                >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                    </svg>
                </button>
                <button
                    onClick={handleFitToScreen}
                    className="p-2 bg-white dark:bg-gray-700 rounded-lg shadow-md hover:shadow-lg transition-shadow"
                    title="Fit to Screen"
                >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4" />
                    </svg>
                </button>
            </div>

            {/* Zoom Level Indicator */}
            <div className="absolute bottom-4 right-4 px-3 py-1 bg-white dark:bg-gray-700 rounded-lg shadow-md text-sm">
                Zoom: {Math.round(zoomLevel * 100)}%
            </div>

            {/* Selected Node Info */}
            {selectedNode && (
                <div className="absolute bottom-4 left-4 p-3 bg-white dark:bg-gray-700 rounded-lg shadow-md">
                    <h4 className="font-semibold text-sm mb-1">Selected Node</h4>
                    <p className="text-sm text-gray-600 dark:text-gray-300">ID: {selectedNode}</p>
                    {nodesData.find(n => n.id === selectedNode)?.label && (
                        <p className="text-sm text-gray-600 dark:text-gray-300">
                            Label: {nodesData.find(n => n.id === selectedNode)?.label}
                        </p>
                    )}
                </div>
            )}
        </div>
    );
};

export default GraphVisualizer;
