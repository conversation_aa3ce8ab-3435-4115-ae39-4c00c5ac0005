import Reset from "../Components/ResetColors";

function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

async function TopologicalSort(prevNodes, onNodeChange, adjList, delay, speedrun, links, onLinkChange) {
    const [resetNodes, resetLinks] = Reset(prevNodes, links);
    onNodeChange(resetNodes);
    onLinkChange(resetLinks);

    // Calculate in-degrees
    const inDegree = {};
    const queue = [];
    const result = [];

    // Initialize in-degrees
    for (const node of prevNodes) {
        inDegree[node.id] = 0;
    }

    // Calculate in-degrees
    for (const nodeId in adjList) {
        for (const neighbor of adjList[nodeId] || []) {
            inDegree[neighbor] = (inDegree[neighbor] || 0) + 1;
        }
    }

    // Find nodes with in-degree 0
    for (const nodeId in inDegree) {
        if (inDegree[nodeId] === 0) {
            queue.push(nodeId);
        }
    }

    if (speedrun.current === 'fast') {
        delay = 100;
    } else if (speedrun.current === 'skip') {
        delay = 0;
    }

    let step = 0;
    while (queue.length > 0) {
        const current = queue.shift();
        result.push(current);
        step++;

        // Mark current node as being processed
        const updated = prevNodes.map(n => {
            if (n.id === current) {
                n.color = 'orange';
                n.label = `${n.id} (${step})`;
            }
            return n;
        });
        onNodeChange([...updated]);

        await sleep(delay);

        // Process neighbors
        for (const neighbor of adjList[current] || []) {
            inDegree[neighbor]--;
            
            // Highlight edge being processed
            const updatedLinks = links.map(link => {
                if ((link.source.id === current && link.target.id === neighbor)) {
                    link.color = 'blue';
                }
                return link;
            });
            onLinkChange([...updatedLinks]);

            if (inDegree[neighbor] === 0) {
                queue.push(neighbor);
            }
        }

        await sleep(delay);

        // Mark node as processed
        const final = updated.map(n => {
            if (n.id === current) {
                n.color = 'green';
            }
            return n;
        });
        onNodeChange([...final]);

        await sleep(delay);
    }

    // Check for cycles
    if (result.length !== prevNodes.length) {
        // Highlight remaining nodes as part of cycle
        const cycleNodes = prevNodes.filter(node => !result.includes(node.id));
        const finalNodes = prevNodes.map(n => {
            if (cycleNodes.some(cn => cn.id === n.id)) {
                n.color = 'red';
                n.label = `${n.id} (cycle)`;
            }
            return n;
        });
        onNodeChange([...finalNodes]);
        
        return { result: [], hasCycle: true };
    }

    return { result, hasCycle: false };
}

export default TopologicalSort;
