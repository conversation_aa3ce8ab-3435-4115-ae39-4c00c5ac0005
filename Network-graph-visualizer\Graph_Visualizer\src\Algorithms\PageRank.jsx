import Reset from "../Components/ResetColors";

function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

async function PageRank(prevNodes, onNodeChange, adjList, delay, speedrun, dampingFactor = 0.85, maxIterations = 50, tolerance = 1e-6) {
    const [resetNodes] = Reset(prevNodes, []);
    onNodeChange(resetNodes);

    const n = prevNodes.length;
    const pageRank = {};
    const newPageRank = {};
    
    // Initialize PageRank values
    for (const node of prevNodes) {
        pageRank[node.id] = 1.0 / n;
        newPageRank[node.id] = 0;
    }

    if (speedrun.current === 'fast') {
        delay = 100;
    } else if (speedrun.current === 'skip') {
        delay = 0;
    }

    let iteration = 0;
    let converged = false;

    while (iteration < maxIterations && !converged) {
        iteration++;
        
        // Reset new PageRank values
        for (const nodeId in newPageRank) {
            newPageRank[nodeId] = (1 - dampingFactor) / n;
        }

        // Calculate PageRank for each node
        for (const nodeId in adjList) {
            const outDegree = adjList[nodeId].length;
            if (outDegree > 0) {
                const contribution = dampingFactor * pageRank[nodeId] / outDegree;
                
                for (const neighbor of adjList[nodeId]) {
                    newPageRank[neighbor] += contribution;
                }
            } else {
                // Handle dangling nodes (no outgoing links)
                const contribution = dampingFactor * pageRank[nodeId] / n;
                for (const neighbor in newPageRank) {
                    newPageRank[neighbor] += contribution;
                }
            }
        }

        // Check for convergence
        let maxDiff = 0;
        for (const nodeId in pageRank) {
            const diff = Math.abs(newPageRank[nodeId] - pageRank[nodeId]);
            maxDiff = Math.max(maxDiff, diff);
            pageRank[nodeId] = newPageRank[nodeId];
        }

        converged = maxDiff < tolerance;

        // Update visualization
        const maxPR = Math.max(...Object.values(pageRank));
        const minPR = Math.min(...Object.values(pageRank));
        const range = maxPR - minPR;

        const updatedNodes = prevNodes.map(n => {
            const pr = pageRank[n.id];
            const normalizedPR = range > 0 ? (pr - minPR) / range : 0.5;
            
            // Color based on PageRank value (red = high, blue = low)
            const intensity = Math.floor(255 * normalizedPR);
            const color = `rgb(${255 - intensity}, ${intensity}, ${intensity})`;
            
            return {
                ...n,
                color: color,
                label: `${n.id} (${pr.toFixed(4)})`,
                size: 15 + normalizedPR * 20 // Vary node size based on PageRank
            };
        });
        
        onNodeChange([...updatedNodes]);
        await sleep(delay);
    }

    // Final highlighting - highest PageRank in gold
    const maxPR = Math.max(...Object.values(pageRank));
    const highestPRNode = Object.keys(pageRank).find(nodeId => pageRank[nodeId] === maxPR);
    
    const finalNodes = prevNodes.map(n => {
        const pr = pageRank[n.id];
        const normalizedPR = (pr - Math.min(...Object.values(pageRank))) / 
                           (Math.max(...Object.values(pageRank)) - Math.min(...Object.values(pageRank)));
        
        return {
            ...n,
            color: n.id === highestPRNode ? 'gold' : `rgb(${255 - Math.floor(255 * normalizedPR)}, ${Math.floor(255 * normalizedPR)}, ${Math.floor(255 * normalizedPR)})`,
            label: `${n.id} (${pr.toFixed(4)})`,
            size: 15 + normalizedPR * 20
        };
    });
    
    onNodeChange([...finalNodes]);

    return { 
        pageRank, 
        iterations: iteration, 
        converged,
        highestNode: highestPRNode,
        highestValue: maxPR
    };
}

export default PageRank;
