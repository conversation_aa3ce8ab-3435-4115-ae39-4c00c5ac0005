import Reset from "../Components/ResetColors";

function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

class UnionFind {
    constructor(nodes) {
        this.parent = {};
        this.rank = {};
        
        for (const node of nodes) {
            this.parent[node.id] = node.id;
            this.rank[node.id] = 0;
        }
    }

    find(x) {
        if (this.parent[x] !== x) {
            this.parent[x] = this.find(this.parent[x]);
        }
        return this.parent[x];
    }

    union(x, y) {
        const rootX = this.find(x);
        const rootY = this.find(y);

        if (rootX !== rootY) {
            if (this.rank[rootX] < this.rank[rootY]) {
                this.parent[rootX] = rootY;
            } else if (this.rank[rootX] > this.rank[rootY]) {
                this.parent[rootY] = rootX;
            } else {
                this.parent[rootY] = rootX;
                this.rank[rootX]++;
            }
            return true;
        }
        return false;
    }
}

async function Kruskal(prevNodes, onNodeChange, links, onLinkChange, delay, speedrun) {
    const [resetNodes, resetLinks] = Reset(prevNodes, links);
    onNodeChange(resetNodes);
    onLinkChange(resetLinks);

    // Get all edges with weights
    const edges = links.map(link => ({
        source: link.source.id || link.source,
        target: link.target.id || link.target,
        weight: link.weight || 1,
        original: link
    }));

    // Sort edges by weight
    edges.sort((a, b) => a.weight - b.weight);

    const unionFind = new UnionFind(prevNodes);
    const mstEdges = [];
    let totalWeight = 0;

    if (speedrun.current === 'fast') {
        delay = 100;
    } else if (speedrun.current === 'skip') {
        delay = 0;
    }

    // Process each edge
    for (let i = 0; i < edges.length; i++) {
        const edge = edges[i];
        
        // Highlight current edge being considered
        const updatedLinks = resetLinks.map(link => {
            if (link === edge.original) {
                link.color = 'orange';
            }
            return link;
        });
        onLinkChange([...updatedLinks]);
        
        await sleep(delay);

        // Check if adding this edge creates a cycle
        if (unionFind.union(edge.source, edge.target)) {
            // Edge is part of MST
            mstEdges.push(edge);
            totalWeight += edge.weight;
            
            // Highlight MST edge
            const finalLinks = updatedLinks.map(link => {
                if (link === edge.original) {
                    link.color = 'green';
                }
                return link;
            });
            onLinkChange([...finalLinks]);
        } else {
            // Edge creates cycle, reject it
            const rejectedLinks = updatedLinks.map(link => {
                if (link === edge.original) {
                    link.color = 'red';
                }
                return link;
            });
            onLinkChange([...rejectedLinks]);
        }

        await sleep(delay);

        // Reset non-MST edges to default color
        const cleanLinks = resetLinks.map(link => {
            const isMSTEdge = mstEdges.some(mstEdge => mstEdge.original === link);
            if (isMSTEdge) {
                link.color = 'green';
            } else if (link === edge.original && !isMSTEdge) {
                link.color = '#999';
            }
            return link;
        });
        onLinkChange([...cleanLinks]);
    }

    // Highlight all MST nodes
    const finalNodes = prevNodes.map(node => ({
        ...node,
        color: 'green'
    }));
    onNodeChange([...finalNodes]);

    return { mstEdges, totalWeight };
}

export default Kruskal;
