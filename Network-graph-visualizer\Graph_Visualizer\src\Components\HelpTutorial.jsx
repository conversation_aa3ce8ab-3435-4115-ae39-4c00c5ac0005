import { useState } from 'react';

const HelpTutorial = () => {
    const [activeTab, setActiveTab] = useState('getting-started');
    const [showTutorial, setShowTutorial] = useState(false);

    const tutorials = {
        'getting-started': {
            title: 'Getting Started',
            content: (
                <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Welcome to Graph Visualizer!</h3>
                    <div className="space-y-3 text-sm text-gray-700 dark:text-gray-300">
                        <p>This tool helps you visualize and analyze graph data structures. Here's how to get started:</p>
                        <ol className="list-decimal list-inside space-y-2">
                            <li><strong>Enter Graph Data:</strong> Use the input panel to enter your graph edges</li>
                            <li><strong>Configure Graph:</strong> Choose if your graph is directed or weighted</li>
                            <li><strong>Visualize:</strong> See your graph rendered with interactive nodes and edges</li>
                            <li><strong>Run Algorithms:</strong> Execute BFS or DFS to see how they traverse your graph</li>
                        </ol>
                    </div>
                </div>
            )
        },
        'input-format': {
            title: 'Input Format',
            content: (
                <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white">How to Enter Graph Data</h3>
                    <div className="space-y-4 text-sm text-gray-700 dark:text-gray-300">
                        <div>
                            <h4 className="font-medium mb-2">Unweighted Graph:</h4>
                            <pre className="bg-gray-100 dark:bg-gray-800 p-3 rounded text-xs">
A B
B C
C D
D A
                            </pre>
                            <p className="mt-1 text-xs">Each line represents an edge between two nodes.</p>
                        </div>
                        <div>
                            <h4 className="font-medium mb-2">Weighted Graph:</h4>
                            <pre className="bg-gray-100 dark:bg-gray-800 p-3 rounded text-xs">
A B 5
B C 3
C D 7
D A 2
                            </pre>
                            <p className="mt-1 text-xs">Add a third number for edge weight.</p>
                        </div>
                        <div>
                            <h4 className="font-medium mb-2">Tips:</h4>
                            <ul className="list-disc list-inside space-y-1 text-xs">
                                <li>Node names can be letters, numbers, or words</li>
                                <li>Use spaces to separate nodes and weights</li>
                                <li>Each line should contain one edge</li>
                                <li>Empty lines are ignored</li>
                            </ul>
                        </div>
                    </div>
                </div>
            )
        },
        'algorithms': {
            title: 'Algorithms',
            content: (
                <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Graph Algorithms</h3>
                    <div className="space-y-4 text-sm text-gray-700 dark:text-gray-300">
                        <div>
                            <h4 className="font-medium mb-2">Breadth-First Search (BFS):</h4>
                            <ul className="list-disc list-inside space-y-1 text-xs">
                                <li>Explores nodes level by level</li>
                                <li>Uses a queue data structure</li>
                                <li>Finds shortest path in unweighted graphs</li>
                                <li>Good for finding connected components</li>
                            </ul>
                        </div>
                        <div>
                            <h4 className="font-medium mb-2">Depth-First Search (DFS):</h4>
                            <ul className="list-disc list-inside space-y-1 text-xs">
                                <li>Explores as far as possible before backtracking</li>
                                <li>Uses recursion or a stack</li>
                                <li>Good for detecting cycles</li>
                                <li>Used in topological sorting</li>
                            </ul>
                        </div>
                        <div>
                            <h4 className="font-medium mb-2">Color Coding:</h4>
                            <div className="flex flex-wrap gap-4 text-xs">
                                <div className="flex items-center">
                                    <div className="w-3 h-3 bg-blue-500 rounded-full mr-2"></div>
                                    <span>Unvisited</span>
                                </div>
                                <div className="flex items-center">
                                    <div className="w-3 h-3 bg-orange-500 rounded-full mr-2"></div>
                                    <span>Visiting</span>
                                </div>
                                <div className="flex items-center">
                                    <div className="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                                    <span>Visited</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            )
        },
        'features': {
            title: 'Features',
            content: (
                <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Key Features</h3>
                    <div className="space-y-3 text-sm text-gray-700 dark:text-gray-300">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <h4 className="font-medium mb-2">Visualization:</h4>
                                <ul className="list-disc list-inside space-y-1 text-xs">
                                    <li>Interactive drag-and-drop nodes</li>
                                    <li>Automatic force-directed layout</li>
                                    <li>Support for directed/undirected graphs</li>
                                    <li>Weighted edge visualization</li>
                                </ul>
                            </div>
                            <div>
                                <h4 className="font-medium mb-2">Analysis:</h4>
                                <ul className="list-disc list-inside space-y-1 text-xs">
                                    <li>Graph statistics and metrics</li>
                                    <li>Connectivity analysis</li>
                                    <li>Component counting</li>
                                    <li>Degree distribution</li>
                                </ul>
                            </div>
                            <div>
                                <h4 className="font-medium mb-2">Algorithms:</h4>
                                <ul className="list-disc list-inside space-y-1 text-xs">
                                    <li>Real-time algorithm visualization</li>
                                    <li>Adjustable animation speed</li>
                                    <li>Step-by-step execution</li>
                                    <li>Pause and resume functionality</li>
                                </ul>
                            </div>
                            <div>
                                <h4 className="font-medium mb-2">Data Management:</h4>
                                <ul className="list-disc list-inside space-y-1 text-xs">
                                    <li>Export graphs in multiple formats</li>
                                    <li>Import from JSON, CSV, or text files</li>
                                    <li>Pre-loaded example graphs</li>
                                    <li>Input validation and formatting</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            )
        }
    };

    const tabs = Object.keys(tutorials);

    return (
        <>
            <button
                onClick={() => setShowTutorial(true)}
                className="flex items-center px-4 py-2 bg-indigo-500 hover:bg-indigo-600 text-white rounded-lg font-medium transition-colors"
            >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                Help & Tutorial
            </button>

            {showTutorial && (
                <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
                    <div className="bg-white dark:bg-gray-800 rounded-lg w-full max-w-4xl max-h-[90vh] overflow-hidden">
                        {/* Header */}
                        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
                            <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
                                Help & Tutorial
                            </h2>
                            <button
                                onClick={() => setShowTutorial(false)}
                                className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                            >
                                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                </svg>
                            </button>
                        </div>

                        <div className="flex h-[calc(90vh-120px)]">
                            {/* Sidebar */}
                            <div className="w-64 border-r border-gray-200 dark:border-gray-700 overflow-y-auto">
                                <nav className="p-4 space-y-2">
                                    {tabs.map((tab) => (
                                        <button
                                            key={tab}
                                            onClick={() => setActiveTab(tab)}
                                            className={`w-full text-left px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                                                activeTab === tab
                                                    ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300'
                                                    : 'text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700'
                                            }`}
                                        >
                                            {tutorials[tab].title}
                                        </button>
                                    ))}
                                </nav>
                            </div>

                            {/* Content */}
                            <div className="flex-1 overflow-y-auto p-6">
                                {tutorials[activeTab].content}
                            </div>
                        </div>
                    </div>
                </div>
            )}
        </>
    );
};

export default HelpTutorial;
