import Reset from "../Components/ResetColors";

function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

async function BellmanFord(src, prevNodes, onNodeChange, links, onLinkChange, delay, speedrun) {
    const [resetNodes, resetLinks] = Reset(prevNodes, links);
    onNodeChange(resetNodes);
    onLinkChange(resetLinks);

    const distances = {};
    const previous = {};
    
    // Initialize distances
    for (const node of prevNodes) {
        distances[node.id] = node.id === src ? 0 : Infinity;
        previous[node.id] = null;
    }

    if (speedrun.current === 'fast') {
        delay = 100;
    } else if (speedrun.current === 'skip') {
        delay = 0;
    }

    // Relax edges V-1 times
    for (let i = 0; i < prevNodes.length - 1; i++) {
        let updated = false;
        
        // Mark current iteration
        const iterationNodes = prevNodes.map(n => ({
            ...n,
            label: `${n.id} (${distances[n.id] === Infinity ? '∞' : distances[n.id]})`
        }));
        onNodeChange([...iterationNodes]);
        
        await sleep(delay);

        for (const link of links) {
            const u = link.source.id || link.source;
            const v = link.target.id || link.target;
            const weight = link.weight || 1;

            // Highlight current edge
            const highlightedLinks = links.map(l => ({
                ...l,
                color: l === link ? 'orange' : (l.color === 'green' ? 'green' : '#999')
            }));
            onLinkChange([...highlightedLinks]);

            await sleep(delay / 2);

            if (distances[u] !== Infinity && distances[u] + weight < distances[v]) {
                distances[v] = distances[u] + weight;
                previous[v] = u;
                updated = true;

                // Highlight relaxed edge
                const relaxedLinks = links.map(l => ({
                    ...l,
                    color: l === link ? 'green' : (l.color === 'green' ? 'green' : '#999')
                }));
                onLinkChange([...relaxedLinks]);

                // Update node label
                const updatedNodes = prevNodes.map(n => ({
                    ...n,
                    label: `${n.id} (${distances[n.id] === Infinity ? '∞' : distances[n.id]})`,
                    color: n.id === v ? 'blue' : n.color
                }));
                onNodeChange([...updatedNodes]);
            }

            await sleep(delay / 2);
        }

        if (!updated) break; // Early termination if no updates
    }

    // Check for negative cycles
    let hasNegativeCycle = false;
    for (const link of links) {
        const u = link.source.id || link.source;
        const v = link.target.id || link.target;
        const weight = link.weight || 1;

        if (distances[u] !== Infinity && distances[u] + weight < distances[v]) {
            hasNegativeCycle = true;
            
            // Highlight negative cycle
            const cycleLinks = links.map(l => ({
                ...l,
                color: l === link ? 'red' : l.color
            }));
            onLinkChange([...cycleLinks]);

            const cycleNodes = prevNodes.map(n => ({
                ...n,
                color: n.id === v ? 'red' : n.color,
                label: `${n.id} (-∞)`
            }));
            onNodeChange([...cycleNodes]);
            break;
        }
    }

    // Final coloring
    if (!hasNegativeCycle) {
        const finalNodes = prevNodes.map(n => ({
            ...n,
            color: n.id === src ? 'red' : 'green',
            label: `${n.id} (${distances[n.id] === Infinity ? '∞' : distances[n.id]})`
        }));
        onNodeChange([...finalNodes]);
    }

    return { distances, hasNegativeCycle, previous };
}

export default BellmanFord;
