import { createContext, useContext, useEffect, useState } from 'react';

const ThemeContext = createContext();

export const useTheme = () => {
    const context = useContext(ThemeContext);
    if (!context) {
        throw new Error('useTheme must be used within a ThemeProvider');
    }
    return context;
};

const themes = {
    light: {
        name: 'Light',
        background: 'bg-gray-50',
        surface: 'bg-white',
        text: 'text-gray-900',
        textSecondary: 'text-gray-600',
        border: 'border-gray-200',
        accent: 'bg-blue-500',
        nodeColor: '#4f46e5',
        linkColor: '#999',
        highlightColor: '#f59e0b'
    },
    dark: {
        name: 'Dark',
        background: 'bg-gray-900',
        surface: 'bg-gray-800',
        text: 'text-gray-100',
        textSecondary: 'text-gray-400',
        border: 'border-gray-700',
        accent: 'bg-blue-600',
        nodeColor: '#6366f1',
        linkColor: '#6b7280',
        highlightColor: '#f59e0b'
    },
    neon: {
        name: 'Neon',
        background: 'bg-black',
        surface: 'bg-gray-900',
        text: 'text-green-400',
        textSecondary: 'text-green-300',
        border: 'border-green-500',
        accent: 'bg-green-500',
        nodeColor: '#10b981',
        linkColor: '#059669',
        highlightColor: '#f59e0b'
    },
    ocean: {
        name: 'Ocean',
        background: 'bg-blue-900',
        surface: 'bg-blue-800',
        text: 'text-blue-100',
        textSecondary: 'text-blue-200',
        border: 'border-blue-600',
        accent: 'bg-cyan-500',
        nodeColor: '#06b6d4',
        linkColor: '#0891b2',
        highlightColor: '#f59e0b'
    },
    sunset: {
        name: 'Sunset',
        background: 'bg-orange-900',
        surface: 'bg-orange-800',
        text: 'text-orange-100',
        textSecondary: 'text-orange-200',
        border: 'border-orange-600',
        accent: 'bg-red-500',
        nodeColor: '#f97316',
        linkColor: '#ea580c',
        highlightColor: '#fbbf24'
    }
};

export const ThemeProvider = ({ children }) => {
    const [currentTheme, setCurrentTheme] = useState('light');
    const [isDarkMode, setIsDarkMode] = useState(false);

    useEffect(() => {
        // Check system preference
        const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
        setIsDarkMode(mediaQuery.matches);
        
        // Load saved theme
        const savedTheme = localStorage.getItem('graph-visualizer-theme');
        if (savedTheme && themes[savedTheme]) {
            setCurrentTheme(savedTheme);
        } else {
            setCurrentTheme(mediaQuery.matches ? 'dark' : 'light');
        }

        const handleChange = (e) => setIsDarkMode(e.matches);
        mediaQuery.addEventListener('change', handleChange);
        return () => mediaQuery.removeEventListener('change', handleChange);
    }, []);

    useEffect(() => {
        // Apply theme to document
        const root = document.documentElement;
        const theme = themes[currentTheme];
        
        // Update CSS custom properties
        root.style.setProperty('--theme-node-color', theme.nodeColor);
        root.style.setProperty('--theme-link-color', theme.linkColor);
        root.style.setProperty('--theme-highlight-color', theme.highlightColor);
        
        // Update dark mode class
        if (currentTheme === 'dark' || currentTheme === 'neon' || currentTheme === 'ocean' || currentTheme === 'sunset') {
            root.classList.add('dark');
        } else {
            root.classList.remove('dark');
        }
        
        // Save theme preference
        localStorage.setItem('graph-visualizer-theme', currentTheme);
    }, [currentTheme]);

    const changeTheme = (themeName) => {
        if (themes[themeName]) {
            setCurrentTheme(themeName);
        }
    };

    const toggleDarkMode = () => {
        setCurrentTheme(currentTheme === 'light' ? 'dark' : 'light');
    };

    const value = {
        currentTheme,
        theme: themes[currentTheme],
        themes,
        changeTheme,
        toggleDarkMode,
        isDarkMode: currentTheme !== 'light'
    };

    return (
        <ThemeContext.Provider value={value}>
            {children}
        </ThemeContext.Provider>
    );
};

export default ThemeProvider;
