{"dependencies": {"bcryptjs": "^2.4.3", "braintree": "^3.21.0", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "firebase": "^10.7.1", "jsonwebtoken": "^9.0.2", "mongoose": "^8.0.3", "nodemon": "^3.0.2"}, "name": "travel-and-tourism-management-system", "version": "1.0.0", "main": "index.js", "type": "module", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "nodemon backend/index.js", "start": "node backend/index.js", "build": "npm install && npm install --prefix client && npm run build --prefix client"}, "keywords": [], "author": "", "license": "ISC", "description": ""}