# Network Graph Visualizer

A modern, interactive web application for visualizing and analyzing graph data structures with support for various graph algorithms.

## 🌟 Features

- **Interactive Graph Visualization**: Drag-and-drop nodes, zoom, and pan functionality
- **Multiple Graph Types**: Support for directed/undirected and weighted/unweighted graphs
- **Algorithm Visualization**:
  - Breadth-First Search (BFS)
  - Depth-First Search (DFS)
- **Real-time Animation**: Watch algorithms execute step-by-step with customizable speed
- **Example Data**: Pre-loaded examples to get started quickly
- **Responsive Design**: Works on desktop and mobile devices
- **Dark Mode Support**: Toggle between light and dark themes

## 🚀 Getting Started

### Prerequisites

- Node.js (version 14 or higher)
- npm or yarn package manager

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd Network-graph-visualizer/Graph_Visualizer
```

2. Install dependencies:
```bash
npm install
```

3. Start the development server:
```bash
npm run dev
```

4. Open your browser and navigate to `http://localhost:5173`

## 📖 Usage

### Input Format

Enter graph edges in the text area using one of these formats:

**Unweighted Graph:**
```
A B
B C
C D
D A
```

**Weighted Graph:**
```
A B 5
B C 3
C D 7
D A 2
```

### Graph Configuration

- **Directed Graph**: Check the "Directed Graph" checkbox for directed edges
- **Weighted Graph**: Check the "Weighted Graph" checkbox to include edge weights

### Running Algorithms

1. Load or enter your graph data
2. Select an algorithm (BFS or DFS)
3. Choose a starting node from the dropdown
4. Set the animation speed (Slow, Normal, Fast)
5. Click "Run Algorithm" to start the visualization

### Color Coding

- 🔵 **Blue**: Unvisited nodes
- 🟠 **Orange**: Currently being visited
- 🟢 **Green**: Visited nodes

## 🛠️ Built With

- **React 19** - Frontend framework
- **D3.js** - Data visualization library
- **Tailwind CSS** - Utility-first CSS framework
- **Vite** - Build tool and development server

## 📁 Project Structure

```
Graph_Visualizer/
├── src/
│   ├── Components/
│   │   ├── GraphVisualizer.jsx    # Main graph visualization component
│   │   ├── AlgorithmControls.jsx  # Algorithm control panel
│   │   ├── ExampleData.jsx        # Example data loader
│   │   ├── BuildAdjList.jsx       # Adjacency list builder
│   │   └── ResetColors.jsx        # Node/edge color reset utility
│   ├── Algorithms/
│   │   ├── Bfs.jsx                # Breadth-First Search implementation
│   │   └── dfs.jsx                # Depth-First Search implementation
│   ├── Hooks/
│   │   └── DataFetch.jsx          # Graph data parsing hook
│   ├── App.jsx                    # Main application component
│   ├── main.jsx                   # Application entry point
│   └── index.css                  # Global styles
├── public/
├── package.json
└── README.md
```

## 🎯 Example Graphs

The application includes several pre-loaded examples:

1. **Simple Undirected Graph** - Basic connectivity demonstration
2. **Directed Graph** - Shows directional relationships
3. **Weighted Undirected Graph** - Includes edge weights/costs
4. **Weighted Directed Graph** - Network with directional costs
5. **Tree Structure** - Hierarchical data representation

## 🔧 Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📝 License

This project is open source and available under the [MIT License](LICENSE).

## 🐛 Known Issues

- Large graphs (>100 nodes) may experience performance issues
- Mobile touch interactions could be improved

## 🚀 Future Enhancements

- [ ] Additional algorithms (Dijkstra's, A*, MST)
- [ ] Graph import/export functionality
- [ ] Performance optimizations for large graphs
- [ ] More customization options
- [ ] Graph analysis metrics

## 📞 Support

If you encounter any issues or have questions, please open an issue on the GitHub repository.

---

Made with ❤️ using React, D3.js, and Tailwind CSS