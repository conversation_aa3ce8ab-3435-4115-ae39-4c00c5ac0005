import { useState } from 'react';

const GraphInputPanel = ({ 
    inputText, 
    setInputText, 
    isDirected, 
    setIsDirected, 
    isWeighted, 
    setIsWeighted,
    onClear,
    nodeCount,
    edgeCount 
}) => {
    const [showHelp, setShowHelp] = useState(false);
    const [validationErrors, setValidationErrors] = useState([]);

    const validateInput = (text) => {
        const errors = [];
        const lines = text.trim().split('\n').filter(line => line.trim());
        
        lines.forEach((line, index) => {
            const parts = line.trim().split(/\s+/);
            
            if (parts.length < 2) {
                errors.push(`Line ${index + 1}: Need at least 2 nodes (source and target)`);
            } else if (isWeighted && parts.length < 3) {
                errors.push(`Line ${index + 1}: Weighted graph requires weight value`);
            } else if (isWeighted && parts.length >= 3 && isNaN(parseFloat(parts[2]))) {
                errors.push(`Line ${index + 1}: Weight must be a number`);
            }
        });
        
        setValidationErrors(errors);
        return errors.length === 0;
    };

    const handleInputChange = (e) => {
        const value = e.target.value;
        setInputText(value);
        if (value.trim()) {
            validateInput(value);
        } else {
            setValidationErrors([]);
        }
    };

    const formatInput = () => {
        const lines = inputText.split('\n').map(line => line.trim()).filter(Boolean);
        const formatted = lines.join('\n');
        setInputText(formatted);
    };

    const exampleFormats = {
        unweighted: "A B\nB C\nC D\nD A",
        weighted: "A B 5\nB C 3\nC D 7\nD A 2",
        directed: "A → B\nB → C\nC → D",
        tree: "Root A\nRoot B\nA C\nA D\nB E\nB F"
    };

    return (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-6">
            <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                    Graph Input
                </h2>
                <div className="flex items-center space-x-2">
                    <button
                        onClick={() => setShowHelp(!showHelp)}
                        className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                        title="Show help"
                    >
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </button>
                    <button
                        onClick={formatInput}
                        className="px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded hover:bg-blue-200 dark:bg-blue-900 dark:text-blue-300"
                        title="Format input"
                    >
                        Format
                    </button>
                    <button
                        onClick={onClear}
                        className="px-3 py-1 text-sm bg-red-100 text-red-700 rounded hover:bg-red-200 dark:bg-red-900 dark:text-red-300"
                        title="Clear input"
                    >
                        Clear
                    </button>
                </div>
            </div>

            {/* Input Area */}
            <div className="mb-4">
                <label className="block text-sm font-medium mb-2 text-gray-700 dark:text-gray-300">
                    Graph Edges
                </label>
                <textarea
                    value={inputText}
                    onChange={handleInputChange}
                    placeholder="Enter graph edges (e.g., A B or A B 5 for weighted)&#10;Example:&#10;A B&#10;B C&#10;C A"
                    className={`w-full h-40 p-3 border rounded-lg font-mono text-sm resize-none transition-colors
                        ${validationErrors.length > 0
                            ? 'border-red-300 dark:border-red-600 bg-red-50 dark:bg-red-900/20'
                            : 'border-gray-300 dark:border-gray-600'
                        }
                        dark:bg-gray-700 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent hover:border-gray-400 dark:hover:border-gray-500`}
                    rows="8"
                />
                
                {/* Validation Errors */}
                {validationErrors.length > 0 && (
                    <div className="mt-2 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded">
                        <h4 className="text-sm font-medium text-red-800 dark:text-red-200 mb-1">Input Errors:</h4>
                        <ul className="text-sm text-red-700 dark:text-red-300 list-disc list-inside">
                            {validationErrors.map((error, index) => (
                                <li key={index}>{error}</li>
                            ))}
                        </ul>
                    </div>
                )}
            </div>

            {/* Graph Configuration */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                <label className="flex items-center space-x-2 p-3 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer">
                    <input
                        type="checkbox"
                        checked={isDirected}
                        onChange={(e) => setIsDirected(e.target.checked)}
                        className="w-4 h-4 text-blue-600 rounded focus:ring-blue-500"
                    />
                    <div>
                        <span className="font-medium text-gray-900 dark:text-white">Directed Graph</span>
                        <p className="text-xs text-gray-500 dark:text-gray-400">Edges have direction</p>
                    </div>
                </label>

                <label className="flex items-center space-x-2 p-3 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer">
                    <input
                        type="checkbox"
                        checked={isWeighted}
                        onChange={(e) => setIsWeighted(e.target.checked)}
                        className="w-4 h-4 text-blue-600 rounded focus:ring-blue-500"
                    />
                    <div>
                        <span className="font-medium text-gray-900 dark:text-white">Weighted Graph</span>
                        <p className="text-xs text-gray-500 dark:text-gray-400">Edges have weights</p>
                    </div>
                </label>

                <div className="p-3 border rounded-lg bg-gray-50 dark:bg-gray-700">
                    <div className="text-sm font-medium text-gray-900 dark:text-white">Graph Stats</div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                        Nodes: {nodeCount} | Edges: {edgeCount}
                    </div>
                </div>
            </div>

            {/* Help Panel */}
            {showHelp && (
                <div className="mt-4 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                    <h3 className="font-semibold text-blue-900 dark:text-blue-100 mb-3">Input Format Guide</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                        <div>
                            <h4 className="font-medium text-blue-800 dark:text-blue-200 mb-1">Unweighted:</h4>
                            <pre className="bg-white dark:bg-gray-800 p-2 rounded text-xs">{exampleFormats.unweighted}</pre>
                        </div>
                        <div>
                            <h4 className="font-medium text-blue-800 dark:text-blue-200 mb-1">Weighted:</h4>
                            <pre className="bg-white dark:bg-gray-800 p-2 rounded text-xs">{exampleFormats.weighted}</pre>
                        </div>
                    </div>
                    <div className="mt-3 text-xs text-blue-700 dark:text-blue-300">
                        <strong>Tips:</strong> Each line represents an edge. Use spaces to separate nodes and weights. 
                        Node names can be letters, numbers, or words.
                    </div>
                </div>
            )}
        </div>
    );
};

export default GraphInputPanel;
