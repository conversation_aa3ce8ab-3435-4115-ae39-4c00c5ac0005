import {useEffect, useState} from 'react';
import './App.css';

import parseGraphInput from './Hooks/DataFetch';
import GraphVisualizer from './Components/GraphVisualizer';
import AlgorithmControls from './Components/AlgorithmControls';
import ExampleData from './Components/ExampleData';
import Header from './Components/Header';
import GraphInputPanel from './Components/GraphInputPanel';
import GraphStatsPanel from './Components/GraphStatsPanel';
import GraphExportImport from './Components/GraphExportImport';
import HelpTutorial from './Components/HelpTutorial';
import ThemeProvider from './Components/ThemeProvider';
import GraphEditor from './Components/GraphEditor';
import GraphTemplates from './Components/GraphTemplates';
import GraphSaveLoad from './Components/GraphSaveLoad';
import QuickStartGuide from './Components/QuickStartGuide';




const App = () => {
  const [nodes, setNodes] = useState([]);
  const [links, setLinks] = useState([]);
  const [adjList, setAdjList] = useState({});
  const [isDirected, setIsDirected] = useState(false);
  const [inputText, setInputText] = useState("A B\nB C\nC D\nD A\nA C");
  const [isWeighted, setIsWeighted] = useState(false);
  const [result, setResult] = useState("");
  const [resultReady, setResultReady] = useState(false);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);

  useEffect(() => {
    const{nodes, links, adjList} = parseGraphInput(inputText, isDirected, isWeighted);
    setNodes(nodes);
    setLinks(links);
    setAdjList(adjList);
  }, [inputText, isDirected, isWeighted]);

  const handleClearInput = () => {
    setInputText("");
    setNodes([]);
    setLinks([]);
    setAdjList({});
    setResult("");
    setResultReady(false);
  };

  return (
    <ThemeProvider>
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 text-gray-900 dark:text-gray-100">
        {/* Header */}
        <Header />

        {/* Main Layout */}
        <div className="flex min-h-[calc(100vh-80px)]">
        {/* Sidebar */}
        <div className={`${sidebarCollapsed ? 'w-16' : 'w-80'} transition-all duration-300 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 overflow-y-auto max-h-[calc(100vh-80px)]`}>
          <div className="p-4">
            {/* Sidebar Toggle */}
            <button
              onClick={() => setSidebarCollapsed(!sidebarCollapsed)}
              className="mb-4 p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
              title={sidebarCollapsed ? 'Expand sidebar' : 'Collapse sidebar'}
            >
              <svg className={`w-5 h-5 transition-transform ${sidebarCollapsed ? 'rotate-180' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 19l-7-7 7-7m8 14l-7-7 7-7" />
              </svg>
            </button>

            {!sidebarCollapsed && (
              <div className="space-y-4">
                {/* Quick Start Guide */}
                <QuickStartGuide
                  setInputText={setInputText}
                  setIsDirected={setIsDirected}
                  setIsWeighted={setIsWeighted}
                />

                {/* Graph Input Panel */}
                <GraphInputPanel
                  inputText={inputText}
                  setInputText={setInputText}
                  isDirected={isDirected}
                  setIsDirected={setIsDirected}
                  isWeighted={isWeighted}
                  setIsWeighted={setIsWeighted}
                  onClear={handleClearInput}
                  nodeCount={nodes.length}
                  edgeCount={links.length}
                />

                {/* Quick Actions */}
                <div className="grid grid-cols-2 gap-2">
                  <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-3">
                    <h3 className="text-sm font-semibold mb-2 text-gray-900 dark:text-white">Templates</h3>
                    <GraphTemplates
                      setInputText={setInputText}
                      setIsDirected={setIsDirected}
                      setIsWeighted={setIsWeighted}
                    />
                  </div>

                  <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-3">
                    <h3 className="text-sm font-semibold mb-2 text-gray-900 dark:text-white">Save/Load</h3>
                    <GraphSaveLoad
                      nodes={nodes}
                      links={links}
                      inputText={inputText}
                      setInputText={setInputText}
                      isDirected={isDirected}
                      setIsDirected={setIsDirected}
                      isWeighted={isWeighted}
                      setIsWeighted={setIsWeighted}
                    />
                  </div>
                </div>

                {/* Example Data */}
                <ExampleData
                  setInputText={setInputText}
                  setIsDirected={setIsDirected}
                  setIsWeighted={setIsWeighted}
                />

                {/* Graph Statistics */}
                {nodes.length > 0 && (
                  <GraphStatsPanel
                    nodes={nodes}
                    links={links}
                    adjList={adjList}
                    isDirected={isDirected}
                    isWeighted={isWeighted}
                    algorithmResult={result}
                  />
                )}

                {/* Export/Import */}
                <GraphExportImport
                  nodes={nodes}
                  links={links}
                  isDirected={isDirected}
                  isWeighted={isWeighted}
                  inputText={inputText}
                  setInputText={setInputText}
                  setIsDirected={setIsDirected}
                  setIsWeighted={setIsWeighted}
                />

                {/* Help Tutorial */}
                <HelpTutorial />
              </div>
            )}
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 flex flex-col overflow-hidden">
          {/* Graph Editor - Compact */}
          {nodes.length > 0 && (
            <div className="p-3 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900">
              <div className="flex items-center justify-between">
                <h3 className="text-sm font-semibold text-gray-900 dark:text-white">Graph Editor</h3>
                <div className="flex gap-2">
                  <button className="px-2 py-1 text-xs bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors">
                    👆 Select
                  </button>
                  <button className="px-2 py-1 text-xs bg-green-500 text-white rounded hover:bg-green-600 transition-colors">
                    ➕ Add Node
                  </button>
                  <button className="px-2 py-1 text-xs bg-purple-500 text-white rounded hover:bg-purple-600 transition-colors">
                    🔗 Add Edge
                  </button>
                  <button className="px-2 py-1 text-xs bg-red-500 text-white rounded hover:bg-red-600 transition-colors">
                    🗑️ Delete
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Algorithm Controls */}
          {nodes.length > 0 && (
            <div className="p-3 border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800">
              <AlgorithmControls
                nodes={nodes}
                setNodes={setNodes}
                links={links}
                setLinks={setLinks}
                adjList={adjList}
                setResult={setResult}
                setResultReady={setResultReady}
              />
            </div>
          )}

          {/* Graph Visualization */}
          <div className="flex-1 p-4 overflow-hidden">
            <div className="h-full min-h-[600px]">
              {nodes.length > 0 ? (
                <>
                  {resultReady && result && (
                    <div className="mb-4 p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
                      <div className="flex items-start text-green-800 dark:text-green-200">
                        <svg className="w-5 h-5 mr-2 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <div>
                          <div className="font-medium">Algorithm completed!</div>
                          <div className="text-sm mt-1">{result}</div>
                        </div>
                      </div>
                    </div>
                  )}

                  <GraphVisualizer
                    nodesData={nodes}
                    linksData={links}
                    isDirected={isDirected}
                    isWeighted={isWeighted}
                  />
                </>
              ) : (
                <div className="h-full min-h-[500px] flex items-center justify-center text-gray-500 dark:text-gray-400 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700">
                  <div className="text-center p-8">
                    <svg className="w-20 h-20 mx-auto mb-6 text-gray-300 dark:text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M13 10V3L4 14h7v7l9-11h-7z" />
                    </svg>
                    <h3 className="text-xl font-medium mb-3 text-gray-700 dark:text-gray-300">No Graph Data</h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">Enter graph edges in the sidebar to start visualizing</p>
                    <div className="text-xs text-gray-500 dark:text-gray-500">
                      <p>Try examples like:</p>
                      <p className="font-mono mt-1">A B<br/>B C<br/>C A</p>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
          </div>
        </div>
      </div>
    </ThemeProvider>
  );
}

export default App