import { useState, useEffect } from 'react';

const GraphStatsPanel = ({ nodes, links, adjList, isDirected, isWeighted, algorithmResult }) => {
    const [stats, setStats] = useState({
        nodeCount: 0,
        edgeCount: 0,
        avgDegree: 0,
        maxDegree: 0,
        minDegree: 0,
        isConnected: false,
        hasCycles: false,
        components: 0
    });

    useEffect(() => {
        calculateStats();
    }, [nodes, links, adjList, isDirected]);

    const calculateStats = () => {
        if (!nodes.length) {
            setStats({
                nodeCount: 0,
                edgeCount: 0,
                avgDegree: 0,
                maxDegree: 0,
                minDegree: 0,
                isConnected: false,
                hasCycles: false,
                components: 0
            });
            return;
        }

        const nodeCount = nodes.length;
        const edgeCount = links.length;

        // Calculate degrees
        const degrees = {};
        nodes.forEach(node => {
            degrees[node.id] = (adjList[node.id] || []).length;
        });

        const degreeValues = Object.values(degrees);
        const maxDegree = Math.max(...degreeValues);
        const minDegree = Math.min(...degreeValues);
        const avgDegree = degreeValues.reduce((sum, deg) => sum + deg, 0) / nodeCount;

        // Check connectivity
        const isConnected = checkConnectivity();
        
        // Count components
        const components = countComponents();

        // Check for cycles (simplified)
        const hasCycles = checkForCycles();

        setStats({
            nodeCount,
            edgeCount,
            avgDegree: avgDegree.toFixed(2),
            maxDegree,
            minDegree,
            isConnected,
            hasCycles,
            components
        });
    };

    const checkConnectivity = () => {
        if (nodes.length <= 1) return true;

        const visited = new Set();
        const queue = [nodes[0].id];
        visited.add(nodes[0].id);

        while (queue.length > 0) {
            const current = queue.shift();
            const neighbors = adjList[current] || [];

            neighbors.forEach(neighbor => {
                const neighborId = typeof neighbor === 'object' ? neighbor.node : neighbor;
                if (!visited.has(neighborId)) {
                    visited.add(neighborId);
                    queue.push(neighborId);
                }
            });
        }

        return visited.size === nodes.length;
    };

    const countComponents = () => {
        const visited = new Set();
        let components = 0;

        nodes.forEach(node => {
            if (!visited.has(node.id)) {
                components++;
                // BFS to mark all nodes in this component
                const queue = [node.id];
                visited.add(node.id);

                while (queue.length > 0) {
                    const current = queue.shift();
                    const neighbors = adjList[current] || [];

                    neighbors.forEach(neighbor => {
                        const neighborId = typeof neighbor === 'object' ? neighbor.node : neighbor;
                        if (!visited.has(neighborId)) {
                            visited.add(neighborId);
                            queue.push(neighborId);
                        }
                    });
                }
            }
        });

        return components;
    };

    const checkForCycles = () => {
        // Simplified cycle detection
        if (isDirected) {
            // For directed graphs, use DFS with recursion stack
            const visited = new Set();
            const recStack = new Set();

            const hasCycleDFS = (node) => {
                visited.add(node);
                recStack.add(node);

                const neighbors = adjList[node] || [];
                for (const neighbor of neighbors) {
                    const neighborId = typeof neighbor === 'object' ? neighbor.node : neighbor;
                    if (!visited.has(neighborId)) {
                        if (hasCycleDFS(neighborId)) return true;
                    } else if (recStack.has(neighborId)) {
                        return true;
                    }
                }

                recStack.delete(node);
                return false;
            };

            for (const node of nodes) {
                if (!visited.has(node.id)) {
                    if (hasCycleDFS(node.id)) return true;
                }
            }
        } else {
            // For undirected graphs, check if edges > nodes - components
            return stats.edgeCount > stats.nodeCount - stats.components;
        }

        return false;
    };

    const StatCard = ({ title, value, description, color = "blue" }) => (
        <div className={`p-4 rounded-lg border-l-4 border-${color}-500 bg-${color}-50 dark:bg-${color}-900/20`}>
            <div className={`text-2xl font-bold text-${color}-700 dark:text-${color}-300`}>
                {value}
            </div>
            <div className={`text-sm font-medium text-${color}-600 dark:text-${color}-400`}>
                {title}
            </div>
            {description && (
                <div className={`text-xs text-${color}-500 dark:text-${color}-500 mt-1`}>
                    {description}
                </div>
            )}
        </div>
    );

    return (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                Graph Statistics
            </h2>

            {/* Basic Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                <StatCard 
                    title="Nodes" 
                    value={stats.nodeCount} 
                    description="Total vertices"
                    color="blue"
                />
                <StatCard 
                    title="Edges" 
                    value={stats.edgeCount} 
                    description="Total connections"
                    color="green"
                />
                <StatCard 
                    title="Components" 
                    value={stats.components} 
                    description="Disconnected parts"
                    color="purple"
                />
                <StatCard 
                    title="Avg Degree" 
                    value={stats.avgDegree} 
                    description="Average connections"
                    color="orange"
                />
            </div>

            {/* Degree Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <h3 className="font-medium text-gray-900 dark:text-white mb-2">Degree Range</h3>
                    <div className="flex justify-between text-sm">
                        <span className="text-gray-600 dark:text-gray-400">Min: {stats.minDegree}</span>
                        <span className="text-gray-600 dark:text-gray-400">Max: {stats.maxDegree}</span>
                    </div>
                </div>

                <div className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <h3 className="font-medium text-gray-900 dark:text-white mb-2">Graph Properties</h3>
                    <div className="space-y-1 text-sm">
                        <div className="flex justify-between">
                            <span className="text-gray-600 dark:text-gray-400">Connected:</span>
                            <span className={stats.isConnected ? 'text-green-600' : 'text-red-600'}>
                                {stats.isConnected ? 'Yes' : 'No'}
                            </span>
                        </div>
                        <div className="flex justify-between">
                            <span className="text-gray-600 dark:text-gray-400">Has Cycles:</span>
                            <span className={stats.hasCycles ? 'text-orange-600' : 'text-green-600'}>
                                {stats.hasCycles ? 'Yes' : 'No'}
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            {/* Algorithm Result */}
            {algorithmResult && (
                <div className="p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                    <h3 className="font-medium text-blue-900 dark:text-blue-100 mb-2">Algorithm Result</h3>
                    <div className="text-sm text-blue-800 dark:text-blue-200">
                        {algorithmResult}
                    </div>
                </div>
            )}

            {/* Graph Type Indicator */}
            <div className="mt-4 flex flex-wrap gap-2">
                <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                    isDirected 
                        ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' 
                        : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200'
                }`}>
                    {isDirected ? 'Directed' : 'Undirected'}
                </span>
                <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                    isWeighted 
                        ? 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200' 
                        : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200'
                }`}>
                    {isWeighted ? 'Weighted' : 'Unweighted'}
                </span>
            </div>
        </div>
    );
};

export default GraphStatsPanel;
