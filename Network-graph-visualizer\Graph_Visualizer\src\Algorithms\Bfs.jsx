import Reset from "../Components/ResetColors";

function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

async function Bfs(src, prevNodes, onNodeChange, adjList, delay, speedrun, links, onLinkChange) {
    
    const[resetNodes, resetLinks] = Reset(prevNodes, links);
    onNodeChange(resetNodes);
    onLinkChange(resetLinks);
    prevNodes = resetNodes;

    const visited = new Set();
    const queue = [src];

    while(queue.length > 0){
        const nodeId = queue.shift();

        if(visited.has(nodeId)) continue;
        visited.add(nodeId);

        // Mark current node as visiting

        const updated = prevNodes.map(n => {
            if(n.id == nodeId){
                n.color = 'orange';
            }
            return n;
        });
        onNodeChange([...updated]);

        if(speedrun.current == 'fast'){
            delay = 100;
        }else if(speedrun.current == 'skip'){
            delay = 0;
        }
        await sleep(delay);

        // Enqueue neighbors

        for(const neighbour of adjList[nodeId] || []){
            if(!visited.has(neighbour)){
                queue.push(neighbour);
            }
        }

        // Mark node as visited

        const final = updated.map(n => {
            if(n.id == nodeId){
                n.color = 'green';
            }
            return n;
        });

        onNodeChange([...final]);

        await sleep(delay);
    }
}

export default Bfs;