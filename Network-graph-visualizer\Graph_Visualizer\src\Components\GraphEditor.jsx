import { useState, useRef } from 'react';

const GraphEditor = ({ nodes, setNodes, links, setLinks, isDirected, isWeighted }) => {
    const [editMode, setEditMode] = useState('select'); // select, addNode, addEdge, delete
    const [selectedNodes, setSelectedNodes] = useState([]);
    const [newNodeName, setNewNodeName] = useState('');
    const [showNodeDialog, setShowNodeDialog] = useState(false);
    const [showEdgeDialog, setShowEdgeDialog] = useState(false);
    const [edgeWeight, setEdgeWeight] = useState(1);
    const [pendingEdge, setPendingEdge] = useState(null);
    const dialogRef = useRef();

    const modes = {
        select: { icon: '👆', label: 'Select', description: 'Select and move nodes' },
        addNode: { icon: '➕', label: 'Add Node', description: 'Click to add new nodes' },
        addEdge: { icon: '🔗', label: 'Add Edge', description: 'Click two nodes to connect them' },
        delete: { icon: '🗑️', label: 'Delete', description: 'Click to delete nodes or edges' }
    };

    const handleNodeClick = (nodeId, event) => {
        event.stopPropagation();
        
        switch (editMode) {
            case 'select':
                if (selectedNodes.includes(nodeId)) {
                    setSelectedNodes(selectedNodes.filter(id => id !== nodeId));
                } else {
                    setSelectedNodes([...selectedNodes, nodeId]);
                }
                break;
                
            case 'addEdge':
                if (!pendingEdge) {
                    setPendingEdge(nodeId);
                } else if (pendingEdge !== nodeId) {
                    // Check if edge already exists
                    const edgeExists = links.some(link => 
                        (link.source.id === pendingEdge && link.target.id === nodeId) ||
                        (!isDirected && link.source.id === nodeId && link.target.id === pendingEdge)
                    );
                    
                    if (!edgeExists) {
                        if (isWeighted) {
                            setShowEdgeDialog(true);
                        } else {
                            addEdge(pendingEdge, nodeId, 1);
                        }
                    }
                    setPendingEdge(null);
                } else {
                    setPendingEdge(null);
                }
                break;
                
            case 'delete':
                deleteNode(nodeId);
                break;
        }
    };

    const handleCanvasClick = (event) => {
        if (editMode === 'addNode') {
            const rect = event.currentTarget.getBoundingClientRect();
            const x = event.clientX - rect.left;
            const y = event.clientY - rect.top;
            setShowNodeDialog(true);
        } else if (editMode === 'select') {
            setSelectedNodes([]);
        }
    };

    const addNode = (name, x = Math.random() * 400 + 200, y = Math.random() * 400 + 200) => {
        if (name && !nodes.find(n => n.id === name)) {
            const newNode = {
                id: name,
                x: x,
                y: y,
                color: '#4f46e5'
            };
            setNodes([...nodes, newNode]);
            setNewNodeName('');
            setShowNodeDialog(false);
        }
    };

    const addEdge = (sourceId, targetId, weight = 1) => {
        const sourceNode = nodes.find(n => n.id === sourceId);
        const targetNode = nodes.find(n => n.id === targetId);
        
        if (sourceNode && targetNode) {
            const newEdge = {
                source: sourceNode,
                target: targetNode,
                weight: isWeighted ? weight : undefined,
                color: '#999'
            };
            setLinks([...links, newEdge]);
            setEdgeWeight(1);
            setShowEdgeDialog(false);
        }
    };

    const deleteNode = (nodeId) => {
        setNodes(nodes.filter(n => n.id !== nodeId));
        setLinks(links.filter(l => l.source.id !== nodeId && l.target.id !== nodeId));
        setSelectedNodes(selectedNodes.filter(id => id !== nodeId));
    };

    const deleteSelectedNodes = () => {
        selectedNodes.forEach(nodeId => deleteNode(nodeId));
        setSelectedNodes([]);
    };

    const duplicateSelectedNodes = () => {
        const newNodes = [];
        selectedNodes.forEach(nodeId => {
            const originalNode = nodes.find(n => n.id === nodeId);
            if (originalNode) {
                let newName = `${nodeId}_copy`;
                let counter = 1;
                while (nodes.find(n => n.id === newName) || newNodes.find(n => n.id === newName)) {
                    newName = `${nodeId}_copy${counter}`;
                    counter++;
                }
                
                newNodes.push({
                    ...originalNode,
                    id: newName,
                    x: originalNode.x + 50,
                    y: originalNode.y + 50
                });
            }
        });
        
        if (newNodes.length > 0) {
            setNodes([...nodes, ...newNodes]);
        }
    };

    return (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-4 mb-4">
            <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Graph Editor</h3>
                
                {/* Mode Selector */}
                <div className="flex gap-2">
                    {Object.entries(modes).map(([mode, config]) => (
                        <button
                            key={mode}
                            onClick={() => {
                                setEditMode(mode);
                                setPendingEdge(null);
                                if (mode !== 'select') setSelectedNodes([]);
                            }}
                            className={`px-3 py-2 text-sm rounded-lg transition-colors ${
                                editMode === mode
                                    ? 'bg-blue-500 text-white'
                                    : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
                            }`}
                            title={config.description}
                        >
                            <span className="mr-1">{config.icon}</span>
                            {config.label}
                        </button>
                    ))}
                </div>
            </div>

            {/* Current Mode Info */}
            <div className="mb-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                <p className="text-sm text-blue-800 dark:text-blue-200">
                    <strong>Mode:</strong> {modes[editMode].description}
                    {pendingEdge && editMode === 'addEdge' && (
                        <span className="ml-2 text-blue-600 dark:text-blue-300">
                            (Selected: {pendingEdge}, click another node to connect)
                        </span>
                    )}
                </p>
            </div>

            {/* Selection Tools */}
            {selectedNodes.length > 0 && (
                <div className="mb-4 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <p className="text-sm text-gray-700 dark:text-gray-300 mb-2">
                        Selected: {selectedNodes.join(', ')}
                    </p>
                    <div className="flex gap-2">
                        <button
                            onClick={duplicateSelectedNodes}
                            className="px-3 py-1 text-xs bg-green-500 text-white rounded hover:bg-green-600 transition-colors"
                        >
                            Duplicate
                        </button>
                        <button
                            onClick={deleteSelectedNodes}
                            className="px-3 py-1 text-xs bg-red-500 text-white rounded hover:bg-red-600 transition-colors"
                        >
                            Delete Selected
                        </button>
                        <button
                            onClick={() => setSelectedNodes([])}
                            className="px-3 py-1 text-xs bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors"
                        >
                            Clear Selection
                        </button>
                    </div>
                </div>
            )}

            {/* Node Dialog */}
            {showNodeDialog && (
                <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                    <div ref={dialogRef} className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-xl">
                        <h4 className="text-lg font-semibold mb-4 text-gray-900 dark:text-white">Add New Node</h4>
                        <input
                            type="text"
                            value={newNodeName}
                            onChange={(e) => setNewNodeName(e.target.value)}
                            placeholder="Node name"
                            className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded mb-4 dark:bg-gray-700 dark:text-white"
                            autoFocus
                            onKeyPress={(e) => e.key === 'Enter' && addNode(newNodeName)}
                        />
                        <div className="flex gap-2">
                            <button
                                onClick={() => addNode(newNodeName)}
                                className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
                                disabled={!newNodeName.trim()}
                            >
                                Add Node
                            </button>
                            <button
                                onClick={() => {
                                    setShowNodeDialog(false);
                                    setNewNodeName('');
                                }}
                                className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors"
                            >
                                Cancel
                            </button>
                        </div>
                    </div>
                </div>
            )}

            {/* Edge Dialog */}
            {showEdgeDialog && (
                <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                    <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-xl">
                        <h4 className="text-lg font-semibold mb-4 text-gray-900 dark:text-white">Add Edge Weight</h4>
                        <input
                            type="number"
                            value={edgeWeight}
                            onChange={(e) => setEdgeWeight(Number(e.target.value))}
                            placeholder="Weight"
                            className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded mb-4 dark:bg-gray-700 dark:text-white"
                            autoFocus
                        />
                        <div className="flex gap-2">
                            <button
                                onClick={() => addEdge(pendingEdge, selectedNodes[0] || '', edgeWeight)}
                                className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
                            >
                                Add Edge
                            </button>
                            <button
                                onClick={() => {
                                    setShowEdgeDialog(false);
                                    setPendingEdge(null);
                                }}
                                className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors"
                            >
                                Cancel
                            </button>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
};

export default GraphEditor;
