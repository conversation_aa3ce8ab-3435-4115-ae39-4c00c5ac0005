import { useState } from 'react';

const ExampleData = ({ setInputText, setIsDirected, setIsWeighted }) => {
    const [isOpen, setIsOpen] = useState(false);

    const examples = [
        {
            name: "Simple Undirected Graph",
            data: "A B\nB C\nC D\nD A\nB D",
            directed: false,
            weighted: false,
            description: "A basic undirected graph with 4 nodes forming a cycle with one diagonal"
        },
        {
            name: "Directed Graph",
            data: "A B\nB C\nC D\nD A\nA C",
            directed: true,
            weighted: false,
            description: "A directed graph showing flow from A to B to C to D and back to A"
        },
        {
            name: "Weighted Undirected Graph",
            data: "A B 5\nB C 3\nC D 7\nD A 2\nA C 4",
            directed: false,
            weighted: true,
            description: "A weighted graph where edges have different costs/distances"
        },
        {
            name: "Weighted Directed Graph",
            data: "A B 10\nB C 5\nC D 3\nD E 8\nE A 6\nA C 15",
            directed: true,
            weighted: true,
            description: "A directed weighted graph representing a network with costs"
        },
        {
            name: "Tree Structure",
            data: "Root A\nRoot B\nA C\nA D\nB E\nB F\nC G\nC H",
            directed: false,
            weighted: false,
            description: "A tree structure with Root as the parent node"
        }
    ];

    const loadExample = (example) => {
        setInputText(example.data);
        setIsDirected(example.directed);
        setIsWeighted(example.weighted);
        setIsOpen(false);
    };

    return (
        <div className="mb-4">
            <button 
                onClick={() => setIsOpen(!isOpen)}
                className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded font-medium"
            >
                {isOpen ? 'Hide Examples' : 'Load Example Data'}
            </button>

            {isOpen && (
                <div className="mt-4 bg-white dark:bg-gray-800 p-4 rounded-lg shadow-md">
                    <h3 className="text-lg font-semibold mb-4">Example Graphs</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {examples.map((example, index) => (
                            <div key={index} className="border dark:border-gray-600 p-3 rounded">
                                <h4 className="font-medium mb-2">{example.name}</h4>
                                <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                                    {example.description}
                                </p>
                                <div className="mb-3">
                                    <pre className="text-xs bg-gray-100 dark:bg-gray-700 p-2 rounded overflow-x-auto">
                                        {example.data}
                                    </pre>
                                </div>
                                <div className="flex gap-2 mb-3 text-xs">
                                    <span className={`px-2 py-1 rounded ${example.directed ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'}`}>
                                        {example.directed ? 'Directed' : 'Undirected'}
                                    </span>
                                    <span className={`px-2 py-1 rounded ${example.weighted ? 'bg-purple-100 text-purple-800' : 'bg-gray-100 text-gray-800'}`}>
                                        {example.weighted ? 'Weighted' : 'Unweighted'}
                                    </span>
                                </div>
                                <button 
                                    onClick={() => loadExample(example)}
                                    className="w-full bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded text-sm"
                                >
                                    Load This Example
                                </button>
                            </div>
                        ))}
                    </div>
                </div>
            )}
        </div>
    );
};

export default ExampleData;
