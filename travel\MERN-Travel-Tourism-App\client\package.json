{"name": "client", "private": true, "version": "0.0.0", "type": "module", "proxy": "http://localhost:8000", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.11.3", "@emotion/styled": "^11.11.0", "@mui/material": "^5.15.5", "@reduxjs/toolkit": "^2.0.1", "axios": "^1.6.4", "braintree-web-drop-in-react": "^1.2.1", "firebase": "^10.7.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hot-toast": "^2.4.1", "react-icons": "^4.12.0", "react-redux": "^9.0.4", "react-router": "^6.21.1", "react-router-dom": "^6.21.1", "react-toastify": "^10.0.4", "recharts": "^2.12.7", "redux-persist": "^6.0.0", "swiper": "^11.0.5", "timeago.js": "^4.0.2"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@vitejs/plugin-react-swc": "^3.5.0", "autoprefixer": "^10.4.16", "eslint": "^8.55.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "postcss": "^8.4.32", "tailwindcss": "^3.4.0", "vite": "^5.0.8"}}