

function buildAdjList(edges, isDirected, isWeighted){
    const adj = {};

    edges.forEach((edge) => {
        const [a, b, weight] = edge;
        if(!adj[a]) adj[a] = [];
        if(!adj[b]) adj[b] = [];

        // For directed graphs, only add edge from a to b
        // For undirected graphs, add edges in both directions
        if (isWeighted) {
            adj[a].push({node: b, weight});
            if (!isDirected) {
                adj[b].push({node: a, weight});
            }
        } else {
            adj[a].push(b);
            if (!isDirected) {
                adj[b].push(a);
            }
        }
    });
    return adj;
}

export default buildAdjList;