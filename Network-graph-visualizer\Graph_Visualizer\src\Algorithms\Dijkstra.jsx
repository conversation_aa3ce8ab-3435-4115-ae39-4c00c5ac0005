import Reset from "../Components/ResetColors";

function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

async function Dijkstra(src, target, prevNodes, onNodeChange, adjList, delay, speedrun, links, onLinkChange) {
    const [resetNodes, resetLinks] = Reset(prevNodes, links);
    onNodeChange(resetNodes);
    onLinkChange(resetLinks);
    prevNodes = resetNodes;

    const distances = {};
    const previous = {};
    const visited = new Set();
    const unvisited = new Set();

    // Initialize distances
    for (const node of prevNodes) {
        distances[node.id] = node.id === src ? 0 : Infinity;
        previous[node.id] = null;
        unvisited.add(node.id);
    }

    while (unvisited.size > 0) {
        // Find unvisited node with minimum distance
        let current = null;
        let minDistance = Infinity;
        
        for (const nodeId of unvisited) {
            if (distances[nodeId] < minDistance) {
                minDistance = distances[nodeId];
                current = nodeId;
            }
        }

        if (current === null || distances[current] === Infinity) break;

        unvisited.delete(current);
        visited.add(current);

        // Mark current node as visiting
        const updated = prevNodes.map(n => {
            if (n.id === current) {
                n.color = 'orange';
                n.label = `${n.id} (${distances[current] === Infinity ? '∞' : distances[current]})`;
            }
            return n;
        });
        onNodeChange([...updated]);

        if (speedrun.current === 'fast') {
            delay = 100;
        } else if (speedrun.current === 'skip') {
            delay = 0;
        }
        await sleep(delay);

        // Update distances to neighbors
        for (const neighbor of adjList[current] || []) {
            if (!visited.has(neighbor)) {
                // Find edge weight
                let weight = 1; // default weight
                const edge = links.find(link => 
                    (link.source.id === current && link.target.id === neighbor) ||
                    (link.target.id === current && link.source.id === neighbor)
                );
                if (edge && edge.weight) {
                    weight = edge.weight;
                }

                const newDistance = distances[current] + weight;
                
                if (newDistance < distances[neighbor]) {
                    distances[neighbor] = newDistance;
                    previous[neighbor] = current;

                    // Highlight the edge being relaxed
                    const updatedLinks = links.map(link => {
                        if ((link.source.id === current && link.target.id === neighbor) ||
                            (link.target.id === current && link.source.id === neighbor)) {
                            link.color = 'blue';
                        }
                        return link;
                    });
                    onLinkChange([...updatedLinks]);
                }
            }
        }

        // Mark node as visited
        const final = updated.map(n => {
            if (n.id === current) {
                n.color = current === target ? 'red' : 'green';
            }
            return n;
        });
        onNodeChange([...final]);

        if (current === target) break;
        await sleep(delay);
    }

    // Highlight shortest path if target is reachable
    if (target && distances[target] !== Infinity) {
        const path = [];
        let current = target;
        while (current !== null) {
            path.unshift(current);
            current = previous[current];
        }

        // Highlight path
        for (let i = 0; i < path.length - 1; i++) {
            const updatedLinks = links.map(link => {
                if ((link.source.id === path[i] && link.target.id === path[i + 1]) ||
                    (link.target.id === path[i] && link.source.id === path[i + 1])) {
                    link.color = 'red';
                }
                return link;
            });
            onLinkChange([...updatedLinks]);
            await sleep(delay);
        }

        return { distances, path, totalDistance: distances[target] };
    }

    return { distances, path: [], totalDistance: Infinity };
}

export default Dijkstra;
