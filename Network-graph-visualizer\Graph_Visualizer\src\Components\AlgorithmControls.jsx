import { useState, useRef } from 'react';
import Bfs from '../Algorithms/Bfs';
import Dfs from '../Algorithms/dfs';
import Dijk<PERSON> from '../Algorithms/Dijkstra';
import Kruskal from '../Algorithms/Kruskal';
import TopologicalSort from '../Algorithms/TopologicalSort';
import BellmanFord from '../Algorithms/BellmanFord';
import PageRank from '../Algorithms/PageRank';

const AlgorithmControls = ({ nodes, setNodes, links, setLinks, adjList, setResult, setResultReady }) => {
    const [selectedAlgorithm, setSelectedAlgorithm] = useState('bfs');
    const [startNode, setStartNode] = useState('');
    const [endNode, setEndNode] = useState('');
    const [isRunning, setIsRunning] = useState(false);
    const [isPaused, setIsPaused] = useState(false);
    const [currentStep, setCurrentStep] = useState(0);
    const [totalSteps, setTotalSteps] = useState(0);
    const [executionTime, setExecutionTime] = useState(0);
    const speedRef = useRef('normal');
    const pauseRef = useRef(false);
    const stepRef = useRef(false);

    const handleRunAlgorithm = async () => {
        // Validate inputs based on algorithm
        const needsStartNode = ['bfs', 'dfs', 'dijkstra', 'bellmanford'].includes(selectedAlgorithm);
        const needsEndNode = ['dijkstra'].includes(selectedAlgorithm);

        if (needsStartNode && (!startNode || !adjList[startNode])) {
            alert('Please enter a valid start node');
            return;
        }

        if (needsEndNode && (!endNode || !adjList[endNode])) {
            alert('Please enter a valid end node');
            return;
        }

        setIsRunning(true);
        setIsPaused(false);
        setResult('');
        setResultReady(false);
        setCurrentStep(0);
        setExecutionTime(0);
        pauseRef.current = false;
        stepRef.current = false;

        const startTime = Date.now();
        const delay = getDelay();

        try {
            let algorithmResult;

            if (selectedAlgorithm === 'bfs') {
                await Bfs(startNode, nodes, setNodes, adjList, delay, speedRef, links, setLinks);
                setResult(`BFS traversal completed starting from node ${startNode}`);
            } else if (selectedAlgorithm === 'dfs') {
                await Dfs(startNode, nodes, setNodes, adjList, delay, speedRef, links, setLinks);
                setResult(`DFS traversal completed starting from node ${startNode}`);
            } else if (selectedAlgorithm === 'dijkstra') {
                if (!endNode || !adjList[endNode]) {
                    alert('Please enter a valid end node for Dijkstra algorithm');
                    setIsRunning(false);
                    return;
                }
                algorithmResult = await Dijkstra(startNode, endNode, nodes, setNodes, adjList, delay, speedRef, links, setLinks);
                setResult(`Shortest path from ${startNode} to ${endNode}: Distance = ${algorithmResult.totalDistance}, Path = ${algorithmResult.path.join(' → ')}`);
            } else if (selectedAlgorithm === 'kruskal') {
                algorithmResult = await Kruskal(nodes, setNodes, links, setLinks, delay, speedRef);
                setResult(`Minimum Spanning Tree completed. Total weight: ${algorithmResult.totalWeight}, Edges: ${algorithmResult.mstEdges.length}`);
            } else if (selectedAlgorithm === 'topological') {
                algorithmResult = await TopologicalSort(nodes, setNodes, adjList, delay, speedRef, links, setLinks);
                if (algorithmResult.hasCycle) {
                    setResult('Topological sort failed: Graph contains cycles');
                } else {
                    setResult(`Topological order: ${algorithmResult.result.join(' → ')}`);
                }
            } else if (selectedAlgorithm === 'bellmanford') {
                algorithmResult = await BellmanFord(startNode, nodes, setNodes, links, setLinks, delay, speedRef);
                if (algorithmResult.hasNegativeCycle) {
                    setResult('Bellman-Ford detected negative cycle in the graph');
                } else {
                    setResult(`Bellman-Ford completed from node ${startNode}. Check node labels for distances.`);
                }
            } else if (selectedAlgorithm === 'pagerank') {
                algorithmResult = await PageRank(nodes, setNodes, adjList, delay, speedRef);
                setResult(`PageRank completed in ${algorithmResult.iterations} iterations. Highest rank: ${algorithmResult.highestNode} (${algorithmResult.highestValue.toFixed(4)})`);
            }

            const endTime = Date.now();
            setExecutionTime(endTime - startTime);
            setResultReady(true);
        } catch (error) {
            console.error('Algorithm execution error:', error);
            setResult(`Error running ${selectedAlgorithm.toUpperCase()}: ${error.message}`);
        }

        setIsRunning(false);
        setIsPaused(false);
    };

    const handlePauseResume = () => {
        if (isPaused) {
            pauseRef.current = false;
            setIsPaused(false);
        } else {
            pauseRef.current = true;
            setIsPaused(true);
        }
    };

    const handleStop = () => {
        setIsRunning(false);
        setIsPaused(false);
        pauseRef.current = false;
        stepRef.current = false;
        // Reset node colors
        const resetNodes = nodes.map(node => ({ ...node, color: '#4f46e5' }));
        setNodes(resetNodes);
        setResult('Algorithm execution stopped');
    };

    const handleStepMode = () => {
        stepRef.current = true;
        if (!isRunning) {
            handleRunAlgorithm();
        }
    };

    const getDelay = () => {
        switch (speedRef.current) {
            case 'fast': return 100;
            case 'slow': return 1000;
            case 'step': return 0;
            default: return 500;
        }
    };

    const handleSpeedChange = (speed) => {
        speedRef.current = speed;
    };

    const nodeIds = nodes.map(node => node.id);

    return (
        <div>
            <div className="flex items-center justify-between mb-4">
                <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
                    Algorithm Controls
                </h2>
                {executionTime > 0 && (
                    <div className="text-sm text-gray-600 dark:text-gray-400">
                        {executionTime}ms
                    </div>
                )}
            </div>

            {/* Algorithm Configuration */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-3 mb-4">
                <div>
                    <label className="block text-sm font-medium mb-2 text-gray-700 dark:text-gray-300">
                        Algorithm
                    </label>
                    <select
                        value={selectedAlgorithm}
                        onChange={(e) => setSelectedAlgorithm(e.target.value)}
                        className="w-full p-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                        disabled={isRunning}
                    >
                        <optgroup label="Graph Traversal">
                            <option value="bfs">Breadth-First Search (BFS)</option>
                            <option value="dfs">Depth-First Search (DFS)</option>
                        </optgroup>
                        <optgroup label="Shortest Path">
                            <option value="dijkstra">Dijkstra's Algorithm</option>
                            <option value="bellmanford">Bellman-Ford Algorithm</option>
                        </optgroup>
                        <optgroup label="Minimum Spanning Tree">
                            <option value="kruskal">Kruskal's Algorithm</option>
                        </optgroup>
                        <optgroup label="Graph Analysis">
                            <option value="topological">Topological Sort</option>
                            <option value="pagerank">PageRank Algorithm</option>
                        </optgroup>
                    </select>
                </div>

                <div>
                    <label className="block text-sm font-medium mb-2 text-gray-700 dark:text-gray-300">
                        Start Node
                    </label>
                    <select
                        value={startNode}
                        onChange={(e) => setStartNode(e.target.value)}
                        className="w-full p-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                        disabled={isRunning}
                    >
                        <option value="">Select start node</option>
                        {nodeIds.map(nodeId => (
                            <option key={nodeId} value={nodeId}>{nodeId}</option>
                        ))}
                    </select>
                </div>

                {(selectedAlgorithm === 'dijkstra') && (
                    <div>
                        <label className="block text-sm font-medium mb-2 text-gray-700 dark:text-gray-300">
                            End Node
                        </label>
                        <select
                            value={endNode}
                            onChange={(e) => setEndNode(e.target.value)}
                            className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                            disabled={isRunning}
                        >
                            <option value="">Select end node</option>
                            {nodeIds.map(nodeId => (
                                <option key={nodeId} value={nodeId}>{nodeId}</option>
                            ))}
                        </select>
                    </div>
                )}

                <div>
                    <label className="block text-sm font-medium mb-2 text-gray-700 dark:text-gray-300">
                        Speed
                    </label>
                    <select
                        onChange={(e) => handleSpeedChange(e.target.value)}
                        className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                        disabled={isRunning && !isPaused}
                    >
                        <option value="slow">Slow (1s)</option>
                        <option value="normal">Normal (0.5s)</option>
                        <option value="fast">Fast (0.1s)</option>
                        <option value="step">Step Mode</option>
                    </select>
                </div>
            </div>

            {/* Control Buttons */}
            <div className="flex flex-wrap gap-3 mb-6">
                <button
                    onClick={handleRunAlgorithm}
                    disabled={isRunning || ((['bfs', 'dfs', 'dijkstra', 'bellmanford'].includes(selectedAlgorithm)) && !startNode)}
                    className="flex items-center px-4 py-2 bg-blue-500 hover:bg-blue-600 disabled:bg-gray-400 text-white rounded-lg font-medium transition-colors"
                >
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h1m4 0h1m6-10V4a2 2 0 00-2-2H5a2 2 0 00-2 2v16l4-2 4 2 4-2 4 2V4z" />
                    </svg>
                    {isRunning ? 'Running...' : 'Run Algorithm'}
                </button>

                {isRunning && (
                    <button
                        onClick={handlePauseResume}
                        className="flex items-center px-4 py-2 bg-yellow-500 hover:bg-yellow-600 text-white rounded-lg font-medium transition-colors"
                    >
                        {isPaused ? (
                            <>
                                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h1m4 0h1m6-10V4a2 2 0 00-2-2H5a2 2 0 00-2 2v16l4-2 4 2 4-2 4 2V4z" />
                                </svg>
                                Resume
                            </>
                        ) : (
                            <>
                                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 9v6m4-6v6m7-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                                Pause
                            </>
                        )}
                    </button>
                )}

                {isRunning && (
                    <button
                        onClick={handleStop}
                        className="flex items-center px-4 py-2 bg-red-500 hover:bg-red-600 text-white rounded-lg font-medium transition-colors"
                    >
                        <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 10h6v4H9z" />
                        </svg>
                        Stop
                    </button>
                )}

                <button
                    onClick={handleStepMode}
                    disabled={isRunning || !startNode}
                    className="flex items-center px-4 py-2 bg-purple-500 hover:bg-purple-600 disabled:bg-gray-400 text-white rounded-lg font-medium transition-colors"
                >
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 9l3 3-3 3m-6 0l3-3-3-3" />
                    </svg>
                    Step Mode
                </button>
            </div>

            {/* Progress Indicator */}
            {isRunning && (
                <div className="mb-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
                    <div className="flex items-center justify-between mb-2">
                        <span className="text-sm font-medium text-blue-900 dark:text-blue-100">
                            Algorithm Progress
                        </span>
                        <span className="text-sm text-blue-700 dark:text-blue-300">
                            {isPaused ? 'Paused' : 'Running'}
                        </span>
                    </div>
                    <div className="w-full bg-blue-200 dark:bg-blue-800 rounded-full h-2">
                        <div
                            className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                            style={{ width: totalSteps > 0 ? `${(currentStep / totalSteps) * 100}%` : '0%' }}
                        ></div>
                    </div>
                </div>
            )}

            {/* Legend */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div className="flex items-center space-x-2">
                    <div className="w-4 h-4 bg-blue-500 rounded-full"></div>
                    <span className="text-gray-600 dark:text-gray-400">Unvisited Nodes</span>
                </div>
                <div className="flex items-center space-x-2">
                    <div className="w-4 h-4 bg-orange-500 rounded-full"></div>
                    <span className="text-gray-600 dark:text-gray-400">Currently Visiting</span>
                </div>
                <div className="flex items-center space-x-2">
                    <div className="w-4 h-4 bg-green-500 rounded-full"></div>
                    <span className="text-gray-600 dark:text-gray-400">Visited Nodes</span>
                </div>
            </div>
        </div>
    );
};

export default AlgorithmControls;
