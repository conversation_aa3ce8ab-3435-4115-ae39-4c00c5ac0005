import { useState, useRef } from 'react';

const GraphSaveLoad = ({ 
    nodes, 
    links, 
    inputText, 
    setInputText, 
    isDirected, 
    setIsDirected, 
    isWeighted, 
    setIsWeighted 
}) => {
    const [savedGraphs, setSavedGraphs] = useState(() => {
        const saved = localStorage.getItem('graph-visualizer-saved-graphs');
        return saved ? JSON.parse(saved) : [];
    });
    const [saveName, setSaveName] = useState('');
    const [showSaveDialog, setShowSaveDialog] = useState(false);
    const [showLoadDialog, setShowLoadDialog] = useState(false);
    const fileInputRef = useRef();

    const saveGraph = () => {
        if (!saveName.trim()) {
            alert('Please enter a name for the graph');
            return;
        }

        const graphData = {
            id: Date.now(),
            name: saveName.trim(),
            inputText,
            isDirected,
            isWeighted,
            nodeCount: nodes.length,
            edgeCount: links.length,
            createdAt: new Date().toISOString()
        };

        const updatedGraphs = [...savedGraphs, graphData];
        setSavedGraphs(updatedGraphs);
        localStorage.setItem('graph-visualizer-saved-graphs', JSON.stringify(updatedGraphs));
        
        setSaveName('');
        setShowSaveDialog(false);
        alert('Graph saved successfully!');
    };

    const loadGraph = (graph) => {
        setInputText(graph.inputText);
        setIsDirected(graph.isDirected);
        setIsWeighted(graph.isWeighted);
        setShowLoadDialog(false);
    };

    const deleteGraph = (graphId) => {
        if (confirm('Are you sure you want to delete this saved graph?')) {
            const updatedGraphs = savedGraphs.filter(g => g.id !== graphId);
            setSavedGraphs(updatedGraphs);
            localStorage.setItem('graph-visualizer-saved-graphs', JSON.stringify(updatedGraphs));
        }
    };

    const exportGraph = (format = 'json') => {
        const graphData = {
            nodes: nodes.map(n => ({ id: n.id, x: n.x, y: n.y })),
            links: links.map(l => ({
                source: l.source.id || l.source,
                target: l.target.id || l.target,
                weight: l.weight
            })),
            isDirected,
            isWeighted,
            inputText,
            exportedAt: new Date().toISOString()
        };

        let content, filename, mimeType;

        switch (format) {
            case 'json':
                content = JSON.stringify(graphData, null, 2);
                filename = 'graph.json';
                mimeType = 'application/json';
                break;
            case 'csv':
                const csvLines = ['source,target' + (isWeighted ? ',weight' : '')];
                links.forEach(link => {
                    const source = link.source.id || link.source;
                    const target = link.target.id || link.target;
                    const weight = link.weight || '';
                    csvLines.push(`${source},${target}${isWeighted ? ',' + weight : ''}`);
                });
                content = csvLines.join('\n');
                filename = 'graph.csv';
                mimeType = 'text/csv';
                break;
            case 'gexf':
                content = generateGEXF(nodes, links, isDirected, isWeighted);
                filename = 'graph.gexf';
                mimeType = 'application/xml';
                break;
            default:
                return;
        }

        const blob = new Blob([content], { type: mimeType });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    };

    const importGraph = (event) => {
        const file = event.target.files[0];
        if (!file) return;

        const reader = new FileReader();
        reader.onload = (e) => {
            try {
                const content = e.target.result;
                
                if (file.name.endsWith('.json')) {
                    const data = JSON.parse(content);
                    if (data.inputText) {
                        setInputText(data.inputText);
                        setIsDirected(data.isDirected || false);
                        setIsWeighted(data.isWeighted || false);
                    }
                } else if (file.name.endsWith('.csv')) {
                    const lines = content.split('\n').filter(line => line.trim());
                    const header = lines[0].toLowerCase();
                    const hasWeight = header.includes('weight');
                    
                    const edges = lines.slice(1).map(line => {
                        const parts = line.split(',');
                        if (hasWeight && parts.length >= 3) {
                            return `${parts[0].trim()} ${parts[1].trim()} ${parts[2].trim()}`;
                        } else {
                            return `${parts[0].trim()} ${parts[1].trim()}`;
                        }
                    });
                    
                    setInputText(edges.join('\n'));
                    setIsWeighted(hasWeight);
                }
                
                alert('Graph imported successfully!');
            } catch (error) {
                alert('Error importing graph: ' + error.message);
            }
        };
        
        reader.readAsText(file);
        event.target.value = '';
    };

    const generateGEXF = (nodes, links, isDirected, isWeighted) => {
        const edgeType = isDirected ? 'directed' : 'undirected';
        let gexf = `<?xml version="1.0" encoding="UTF-8"?>
<gexf xmlns="http://www.gexf.net/1.2draft" version="1.2">
    <graph mode="static" defaultedgetype="${edgeType}">
        <nodes>`;
        
        nodes.forEach(node => {
            gexf += `
            <node id="${node.id}" label="${node.id}"/>`;
        });
        
        gexf += `
        </nodes>
        <edges>`;
        
        links.forEach((link, index) => {
            const source = link.source.id || link.source;
            const target = link.target.id || link.target;
            const weight = isWeighted && link.weight ? ` weight="${link.weight}"` : '';
            gexf += `
            <edge id="${index}" source="${source}" target="${target}"${weight}/>`;
        });
        
        gexf += `
        </edges>
    </graph>
</gexf>`;
        
        return gexf;
    };

    return (
        <div>
            {/* Action Buttons */}
            <div className="grid grid-cols-2 gap-2 mb-3">
                <button
                    onClick={() => setShowSaveDialog(true)}
                    disabled={nodes.length === 0}
                    className="flex items-center justify-center gap-1 px-2 py-1 text-xs bg-blue-500 text-white rounded hover:bg-blue-600 disabled:bg-gray-400 transition-colors"
                >
                    <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3-3m0 0l-3 3m3-3v12" />
                    </svg>
                    Save
                </button>

                <button
                    onClick={() => setShowLoadDialog(true)}
                    disabled={savedGraphs.length === 0}
                    className="flex items-center justify-center gap-1 px-2 py-1 text-xs bg-green-500 text-white rounded hover:bg-green-600 disabled:bg-gray-400 transition-colors"
                >
                    <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12" />
                    </svg>
                    Load
                </button>
            </div>

            <div className="grid grid-cols-2 gap-2">
                <div className="relative">
                    <button
                        onClick={() => fileInputRef.current?.click()}
                        className="w-full flex items-center justify-center gap-1 px-2 py-1 text-xs bg-purple-500 text-white rounded hover:bg-purple-600 transition-colors"
                    >
                        <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10" />
                        </svg>
                        Import
                    </button>
                    <input
                        ref={fileInputRef}
                        type="file"
                        accept=".json,.csv"
                        onChange={importGraph}
                        className="hidden"
                    />
                </div>

                <div className="relative group">
                    <button className="w-full flex items-center justify-center gap-1 px-2 py-1 text-xs bg-orange-500 text-white rounded hover:bg-orange-600 transition-colors">
                        <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                        Export
                    </button>
                    <div className="absolute bottom-full left-0 mb-2 hidden group-hover:block bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg shadow-lg z-10">
                        <button
                            onClick={() => exportGraph('json')}
                            className="block w-full px-3 py-1 text-left text-xs hover:bg-gray-100 dark:hover:bg-gray-600 rounded-t-lg"
                        >
                            JSON
                        </button>
                        <button
                            onClick={() => exportGraph('csv')}
                            className="block w-full px-3 py-1 text-left text-xs hover:bg-gray-100 dark:hover:bg-gray-600"
                        >
                            CSV
                        </button>
                        <button
                            onClick={() => exportGraph('gexf')}
                            className="block w-full px-3 py-1 text-left text-xs hover:bg-gray-100 dark:hover:bg-gray-600 rounded-b-lg"
                        >
                            GEXF
                        </button>
                    </div>
                </div>
            </div>

            {/* Save Dialog */}
            {showSaveDialog && (
                <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                    <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-xl max-w-md w-full mx-4">
                        <h3 className="text-lg font-semibold mb-4 text-gray-900 dark:text-white">Save Graph</h3>
                        <input
                            type="text"
                            value={saveName}
                            onChange={(e) => setSaveName(e.target.value)}
                            placeholder="Enter graph name"
                            className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg mb-4 dark:bg-gray-700 dark:text-white"
                            autoFocus
                            onKeyPress={(e) => e.key === 'Enter' && saveGraph()}
                        />
                        <div className="flex gap-3">
                            <button
                                onClick={saveGraph}
                                className="flex-1 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
                            >
                                Save
                            </button>
                            <button
                                onClick={() => {
                                    setShowSaveDialog(false);
                                    setSaveName('');
                                }}
                                className="flex-1 px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors"
                            >
                                Cancel
                            </button>
                        </div>
                    </div>
                </div>
            )}

            {/* Load Dialog */}
            {showLoadDialog && (
                <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                    <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-96 overflow-y-auto">
                        <h3 className="text-lg font-semibold mb-4 text-gray-900 dark:text-white">Load Saved Graph</h3>
                        
                        {savedGraphs.length === 0 ? (
                            <p className="text-gray-600 dark:text-gray-400 text-center py-8">
                                No saved graphs found
                            </p>
                        ) : (
                            <div className="space-y-3">
                                {savedGraphs.map(graph => (
                                    <div key={graph.id} className="flex items-center justify-between p-3 border border-gray-200 dark:border-gray-600 rounded-lg">
                                        <div className="flex-1">
                                            <h4 className="font-medium text-gray-900 dark:text-white">{graph.name}</h4>
                                            <p className="text-sm text-gray-600 dark:text-gray-400">
                                                {graph.nodeCount} nodes, {graph.edgeCount} edges
                                                {graph.isDirected && ' • Directed'}
                                                {graph.isWeighted && ' • Weighted'}
                                            </p>
                                            <p className="text-xs text-gray-500 dark:text-gray-500">
                                                {new Date(graph.createdAt).toLocaleDateString()}
                                            </p>
                                        </div>
                                        <div className="flex gap-2">
                                            <button
                                                onClick={() => loadGraph(graph)}
                                                className="px-3 py-1 text-sm bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
                                            >
                                                Load
                                            </button>
                                            <button
                                                onClick={() => deleteGraph(graph.id)}
                                                className="px-3 py-1 text-sm bg-red-500 text-white rounded hover:bg-red-600 transition-colors"
                                            >
                                                Delete
                                            </button>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        )}
                        
                        <div className="mt-6 flex justify-end">
                            <button
                                onClick={() => setShowLoadDialog(false)}
                                className="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors"
                            >
                                Close
                            </button>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
};

export default GraphSaveLoad;
