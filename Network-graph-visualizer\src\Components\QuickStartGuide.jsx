import { useState } from 'react';

const QuickStartGuide = ({ setInputText, setIsDirected, setIsWeighted }) => {
    const [isOpen, setIsOpen] = useState(false);

    const quickExamples = [
        {
            name: "Simple Triangle",
            description: "Basic undirected graph",
            data: "A B\nB C\nC A",
            directed: false,
            weighted: false
        },
        {
            name: "Weighted Path",
            description: "Path with edge weights",
            data: "Start Middle 5\nMiddle End 3",
            directed: false,
            weighted: true
        },
        {
            name: "Directed Tree",
            description: "Hierarchical structure",
            data: "Root → Left\nRoot → Right\nLeft → Child1\nRight → Child2",
            directed: true,
            weighted: false
        }
    ];

    const loadExample = (example) => {
        setInputText(example.data);
        setIsDirected(example.directed);
        setIsWeighted(example.weighted);
        setIsOpen(false);
    };

    return (
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg p-4 border border-blue-200 dark:border-blue-800">
            <div className="flex items-center justify-between mb-3">
                <div className="flex items-center gap-2">
                    <svg className="w-5 h-5 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <h3 className="text-sm font-semibold text-blue-900 dark:text-blue-100">Quick Start</h3>
                </div>
                <button
                    onClick={() => setIsOpen(!isOpen)}
                    className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200 transition-colors"
                >
                    <svg className={`w-4 h-4 transition-transform ${isOpen ? 'rotate-180' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                </button>
            </div>

            {isOpen ? (
                <div className="space-y-3">
                    <div className="text-sm text-blue-800 dark:text-blue-200">
                        <p className="mb-2">Welcome to Graph Visualizer! Here's how to get started:</p>
                        <ol className="list-decimal list-inside space-y-1 text-xs">
                            <li>Enter graph edges in the text area (e.g., "A B" for an edge from A to B)</li>
                            <li>For weighted graphs, add weights: "A B 5"</li>
                            <li>Use "→" for directed edges: "A → B"</li>
                            <li>Select an algorithm and click "Run Algorithm"</li>
                            <li>Watch the visualization animate!</li>
                        </ol>
                    </div>

                    <div className="border-t border-blue-200 dark:border-blue-700 pt-3">
                        <p className="text-xs font-medium text-blue-900 dark:text-blue-100 mb-2">Try these examples:</p>
                        <div className="grid grid-cols-1 gap-2">
                            {quickExamples.map((example, index) => (
                                <button
                                    key={index}
                                    onClick={() => loadExample(example)}
                                    className="text-left p-2 bg-white dark:bg-gray-800 rounded border border-blue-200 dark:border-blue-700 hover:bg-blue-50 dark:hover:bg-blue-900/30 transition-colors"
                                >
                                    <div className="flex items-center justify-between">
                                        <span className="text-xs font-medium text-gray-900 dark:text-white">{example.name}</span>
                                        <div className="flex gap-1">
                                            {example.directed && (
                                                <span className="px-1 py-0.5 text-xs bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded">D</span>
                                            )}
                                            {example.weighted && (
                                                <span className="px-1 py-0.5 text-xs bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 rounded">W</span>
                                            )}
                                        </div>
                                    </div>
                                    <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">{example.description}</p>
                                </button>
                            ))}
                        </div>
                    </div>
                </div>
            ) : (
                <div className="text-sm text-blue-800 dark:text-blue-200">
                    <p>New to Graph Visualizer? Click to see quick examples and tips!</p>
                </div>
            )}
        </div>
    );
};

export default QuickStartGuide;
