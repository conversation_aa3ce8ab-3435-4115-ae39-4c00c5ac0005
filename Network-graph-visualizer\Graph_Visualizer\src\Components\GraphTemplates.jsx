import { useState } from 'react';

const GraphTemplates = ({ setInputText, setIsDirected, setIsWeighted }) => {
    const [selectedCategory, setSelectedCategory] = useState('basic');

    const templates = {
        basic: {
            name: 'Basic Graphs',
            templates: {
                triangle: {
                    name: 'Triangle',
                    description: 'Simple 3-node triangle',
                    data: 'A B\nB C\nC A',
                    directed: false,
                    weighted: false
                },
                square: {
                    name: 'Square',
                    description: '4-node square cycle',
                    data: 'A B\nB C\nC D\nD A',
                    directed: false,
                    weighted: false
                },
                star: {
                    name: 'Star',
                    description: 'Central node connected to 5 others',
                    data: 'Center A\nCenter B\nCenter C\nCenter D\nCenter E',
                    directed: false,
                    weighted: false
                },
                line: {
                    name: 'Path',
                    description: 'Linear path of nodes',
                    data: 'A B\nB C\nC D\nD E',
                    directed: false,
                    weighted: false
                }
            }
        },
        trees: {
            name: 'Tree Structures',
            templates: {
                binaryTree: {
                    name: 'Binary Tree',
                    description: 'Complete binary tree',
                    data: 'Root Left\nRoot Right\nLeft LL\nLeft LR\nRight RL\nRight RR',
                    directed: true,
                    weighted: false
                },
                familyTree: {
                    name: 'Family Tree',
                    description: 'Hierarchical family structure',
                    data: 'Grandpa Dad\nGrandpa Uncle\nDad Child1\nDad Child2\nUncle Cousin1\nUncle Cousin2',
                    directed: true,
                    weighted: false
                },
                orgChart: {
                    name: 'Organization Chart',
                    description: 'Corporate hierarchy',
                    data: 'CEO CTO\nCEO CFO\nCTO DevLead\nCTO QALead\nCFO Accountant\nDevLead Dev1\nDevLead Dev2',
                    directed: true,
                    weighted: false
                }
            }
        },
        networks: {
            name: 'Network Graphs',
            templates: {
                socialNetwork: {
                    name: 'Social Network',
                    description: 'Friend connections',
                    data: 'Alice Bob\nAlice Charlie\nBob David\nCharlie David\nCharlie Eve\nDavid Eve\nEve Frank',
                    directed: false,
                    weighted: false
                },
                cityNetwork: {
                    name: 'City Network',
                    description: 'Cities with distances',
                    data: 'NYC Boston 215\nNYC Philadelphia 95\nBoston Philadelphia 310\nPhiladelphia Washington 140\nWashington Atlanta 640\nAtlanta Miami 650',
                    directed: false,
                    weighted: true
                },
                webPages: {
                    name: 'Web Pages',
                    description: 'Website link structure',
                    data: 'Home → About\nHome → Products\nHome → Contact\nProducts → Product1\nProducts → Product2\nAbout → Team\nContact → Support',
                    directed: true,
                    weighted: false
                }
            }
        },
        algorithms: {
            name: 'Algorithm Examples',
            templates: {
                dijkstraExample: {
                    name: 'Dijkstra Example',
                    description: 'Weighted graph for shortest path',
                    data: 'A B 4\nA C 2\nB C 1\nB D 5\nC D 8\nC E 10\nD E 2\nD F 6\nE F 3',
                    directed: false,
                    weighted: true
                },
                topologicalSort: {
                    name: 'Topological Sort',
                    description: 'DAG for topological ordering',
                    data: 'Course1 → Course3\nCourse2 → Course3\nCourse3 → Course4\nCourse3 → Course5\nCourse4 → Course6\nCourse5 → Course6',
                    directed: true,
                    weighted: false
                },
                mstExample: {
                    name: 'MST Example',
                    description: 'Graph for minimum spanning tree',
                    data: 'A B 1\nA C 4\nB C 2\nB D 6\nC D 3\nC E 5\nD E 1\nD F 2\nE F 4',
                    directed: false,
                    weighted: true
                },
                flowNetwork: {
                    name: 'Flow Network',
                    description: 'Network flow example',
                    data: 'Source A 10\nSource B 8\nA C 5\nA D 8\nB C 3\nB E 5\nC Sink 7\nD Sink 6\nE Sink 8',
                    directed: true,
                    weighted: true
                }
            }
        }
    };

    const loadTemplate = (template) => {
        setInputText(template.data);
        setIsDirected(template.directed);
        setIsWeighted(template.weighted);
    };

    return (
        <div>
            {/* Category Selector */}
            <div className="flex flex-wrap gap-1 mb-3">
                {Object.entries(templates).map(([key, category]) => (
                    <button
                        key={key}
                        onClick={() => setSelectedCategory(key)}
                        className={`px-2 py-1 text-xs rounded transition-colors ${
                            selectedCategory === key
                                ? 'bg-blue-500 text-white'
                                : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
                        }`}
                    >
                        {category.name}
                    </button>
                ))}
            </div>

            {/* Template List */}
            <div className="space-y-2 max-h-40 overflow-y-auto">
                {Object.entries(templates[selectedCategory].templates).map(([key, template]) => (
                    <button
                        key={key}
                        className="w-full text-left border border-gray-200 dark:border-gray-600 rounded p-2 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                        onClick={() => loadTemplate(template)}
                    >
                        <div className="flex items-center justify-between">
                            <span className="text-sm font-medium text-gray-900 dark:text-white">
                                {template.name}
                            </span>
                            <div className="flex gap-1">
                                {template.directed && (
                                    <span className="px-1 py-0.5 text-xs bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded">
                                        D
                                    </span>
                                )}
                                {template.weighted && (
                                    <span className="px-1 py-0.5 text-xs bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 rounded">
                                        W
                                    </span>
                                )}
                            </div>
                        </div>
                        <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                            {template.description}
                        </p>
                    </button>
                ))}
            </div>

            {/* Custom Template Creator */}
            <div className="mt-6 p-4 border-t border-gray-200 dark:border-gray-600">
                <h3 className="font-semibold text-gray-900 dark:text-white mb-2">
                    Quick Generators
                </h3>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                    <button
                        onClick={() => {
                            const size = prompt('Enter number of nodes (3-10):');
                            const n = Math.min(Math.max(parseInt(size) || 5, 3), 10);
                            const nodes = Array.from({length: n}, (_, i) => String.fromCharCode(65 + i));
                            const edges = [];
                            for (let i = 0; i < n; i++) {
                                edges.push(`${nodes[i]} ${nodes[(i + 1) % n]}`);
                            }
                            setInputText(edges.join('\n'));
                            setIsDirected(false);
                            setIsWeighted(false);
                        }}
                        className="px-3 py-2 text-sm bg-purple-500 text-white rounded hover:bg-purple-600 transition-colors"
                    >
                        Cycle
                    </button>
                    <button
                        onClick={() => {
                            const size = prompt('Enter number of nodes (3-10):');
                            const n = Math.min(Math.max(parseInt(size) || 5, 3), 10);
                            const nodes = Array.from({length: n}, (_, i) => String.fromCharCode(65 + i));
                            const edges = [];
                            for (let i = 0; i < n; i++) {
                                for (let j = i + 1; j < n; j++) {
                                    edges.push(`${nodes[i]} ${nodes[j]}`);
                                }
                            }
                            setInputText(edges.join('\n'));
                            setIsDirected(false);
                            setIsWeighted(false);
                        }}
                        className="px-3 py-2 text-sm bg-orange-500 text-white rounded hover:bg-orange-600 transition-colors"
                    >
                        Complete
                    </button>
                    <button
                        onClick={() => {
                            const size = prompt('Enter number of leaf nodes (3-8):');
                            const n = Math.min(Math.max(parseInt(size) || 5, 3), 8);
                            const nodes = Array.from({length: n}, (_, i) => String.fromCharCode(65 + i));
                            const edges = nodes.map(node => `Center ${node}`);
                            setInputText(edges.join('\n'));
                            setIsDirected(false);
                            setIsWeighted(false);
                        }}
                        className="px-3 py-2 text-sm bg-green-500 text-white rounded hover:bg-green-600 transition-colors"
                    >
                        Star
                    </button>
                    <button
                        onClick={() => {
                            const size = prompt('Enter number of nodes (3-10):');
                            const n = Math.min(Math.max(parseInt(size) || 5, 3), 10);
                            const nodes = Array.from({length: n}, (_, i) => String.fromCharCode(65 + i));
                            const edges = [];
                            for (let i = 0; i < n - 1; i++) {
                                edges.push(`${nodes[i]} ${nodes[i + 1]}`);
                            }
                            setInputText(edges.join('\n'));
                            setIsDirected(false);
                            setIsWeighted(false);
                        }}
                        className="px-3 py-2 text-sm bg-red-500 text-white rounded hover:bg-red-600 transition-colors"
                    >
                        Path
                    </button>
                </div>
            </div>
        </div>
    );
};

export default GraphTemplates;
