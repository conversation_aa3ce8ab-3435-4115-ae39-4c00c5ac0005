import { useState, useRef } from 'react';

const GraphExportImport = ({ 
    nodes, 
    links, 
    isDirected, 
    isWeighted, 
    inputText,
    setInputText,
    setIsDirected,
    setIsWeighted 
}) => {
    const [showExportModal, setShowExportModal] = useState(false);
    const [showImportModal, setShowImportModal] = useState(false);
    const [importData, setImportData] = useState('');
    const [exportFormat, setExportFormat] = useState('json');
    const fileInputRef = useRef(null);

    const exportGraph = () => {
        const graphData = {
            metadata: {
                name: `Graph_${new Date().toISOString().split('T')[0]}`,
                created: new Date().toISOString(),
                nodeCount: nodes.length,
                edgeCount: links.length,
                isDirected,
                isWeighted
            },
            nodes: nodes.map(node => ({
                id: node.id,
                color: node.color
            })),
            links: links.map(link => ({
                source: typeof link.source === 'object' ? link.source.id : link.source,
                target: typeof link.target === 'object' ? link.target.id : link.target,
                weight: link.weight
            })),
            rawInput: inputText,
            settings: {
                isDirected,
                isWeighted
            }
        };

        let content, filename, mimeType;

        switch (exportFormat) {
            case 'json':
                content = JSON.stringify(graphData, null, 2);
                filename = `graph_${Date.now()}.json`;
                mimeType = 'application/json';
                break;
            case 'csv':
                content = generateCSV();
                filename = `graph_${Date.now()}.csv`;
                mimeType = 'text/csv';
                break;
            case 'txt':
                content = inputText;
                filename = `graph_${Date.now()}.txt`;
                mimeType = 'text/plain';
                break;
            default:
                content = JSON.stringify(graphData, null, 2);
                filename = `graph_${Date.now()}.json`;
                mimeType = 'application/json';
        }

        const blob = new Blob([content], { type: mimeType });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        setShowExportModal(false);
    };

    const generateCSV = () => {
        const headers = isWeighted ? 'Source,Target,Weight' : 'Source,Target';
        const rows = links.map(link => {
            const source = typeof link.source === 'object' ? link.source.id : link.source;
            const target = typeof link.target === 'object' ? link.target.id : link.target;
            return isWeighted ? `${source},${target},${link.weight || ''}` : `${source},${target}`;
        });
        return [headers, ...rows].join('\n');
    };

    const importGraph = () => {
        try {
            const data = JSON.parse(importData);
            
            if (data.rawInput) {
                setInputText(data.rawInput);
            } else if (data.links) {
                // Convert links back to text format
                const textLines = data.links.map(link => {
                    if (data.settings?.isWeighted && link.weight !== undefined) {
                        return `${link.source} ${link.target} ${link.weight}`;
                    }
                    return `${link.source} ${link.target}`;
                });
                setInputText(textLines.join('\n'));
            }
            
            if (data.settings) {
                setIsDirected(data.settings.isDirected || false);
                setIsWeighted(data.settings.isWeighted || false);
            }
            
            setShowImportModal(false);
            setImportData('');
        } catch (error) {
            alert('Invalid JSON format. Please check your data.');
        }
    };

    const handleFileImport = (event) => {
        const file = event.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = (e) => {
                const content = e.target.result;
                
                if (file.name.endsWith('.json')) {
                    setImportData(content);
                } else if (file.name.endsWith('.csv')) {
                    // Parse CSV and convert to text format
                    const lines = content.split('\n').filter(line => line.trim());
                    const hasHeaders = lines[0].toLowerCase().includes('source');
                    const dataLines = hasHeaders ? lines.slice(1) : lines;
                    
                    const textLines = dataLines.map(line => {
                        const parts = line.split(',').map(part => part.trim());
                        return parts.join(' ');
                    });
                    
                    setInputText(textLines.join('\n'));
                    setShowImportModal(false);
                } else {
                    // Treat as plain text
                    setInputText(content);
                    setShowImportModal(false);
                }
            };
            reader.readAsText(file);
        }
        event.target.value = '';
    };

    return (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                Export / Import
            </h2>

            <div className="flex flex-wrap gap-3">
                <button
                    onClick={() => setShowExportModal(true)}
                    disabled={nodes.length === 0}
                    className="flex items-center px-4 py-2 bg-green-500 hover:bg-green-600 disabled:bg-gray-400 text-white rounded-lg font-medium transition-colors"
                >
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    Export Graph
                </button>

                <button
                    onClick={() => setShowImportModal(true)}
                    className="flex items-center px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg font-medium transition-colors"
                >
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10" />
                    </svg>
                    Import Graph
                </button>

                <input
                    type="file"
                    ref={fileInputRef}
                    onChange={handleFileImport}
                    accept=".json,.csv,.txt"
                    className="hidden"
                />

                <button
                    onClick={() => fileInputRef.current?.click()}
                    className="flex items-center px-4 py-2 bg-purple-500 hover:bg-purple-600 text-white rounded-lg font-medium transition-colors"
                >
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    Import File
                </button>
            </div>

            {/* Export Modal */}
            {showExportModal && (
                <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                    <div className="bg-white dark:bg-gray-800 rounded-lg p-6 w-96 max-w-full mx-4">
                        <h3 className="text-lg font-semibold mb-4 text-gray-900 dark:text-white">Export Graph</h3>
                        
                        <div className="mb-4">
                            <label className="block text-sm font-medium mb-2 text-gray-700 dark:text-gray-300">
                                Export Format
                            </label>
                            <select
                                value={exportFormat}
                                onChange={(e) => setExportFormat(e.target.value)}
                                className="w-full p-2 border border-gray-300 rounded-lg dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                            >
                                <option value="json">JSON (Complete data)</option>
                                <option value="csv">CSV (Edge list)</option>
                                <option value="txt">TXT (Raw input)</option>
                            </select>
                        </div>

                        <div className="flex justify-end space-x-3">
                            <button
                                onClick={() => setShowExportModal(false)}
                                className="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200"
                            >
                                Cancel
                            </button>
                            <button
                                onClick={exportGraph}
                                className="px-4 py-2 bg-green-500 hover:bg-green-600 text-white rounded-lg"
                            >
                                Export
                            </button>
                        </div>
                    </div>
                </div>
            )}

            {/* Import Modal */}
            {showImportModal && (
                <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                    <div className="bg-white dark:bg-gray-800 rounded-lg p-6 w-96 max-w-full mx-4">
                        <h3 className="text-lg font-semibold mb-4 text-gray-900 dark:text-white">Import Graph</h3>
                        
                        <div className="mb-4">
                            <label className="block text-sm font-medium mb-2 text-gray-700 dark:text-gray-300">
                                Paste JSON Data
                            </label>
                            <textarea
                                value={importData}
                                onChange={(e) => setImportData(e.target.value)}
                                placeholder="Paste exported JSON data here..."
                                className="w-full h-32 p-3 border border-gray-300 rounded-lg dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                            />
                        </div>

                        <div className="flex justify-end space-x-3">
                            <button
                                onClick={() => {
                                    setShowImportModal(false);
                                    setImportData('');
                                }}
                                className="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200"
                            >
                                Cancel
                            </button>
                            <button
                                onClick={importGraph}
                                disabled={!importData.trim()}
                                className="px-4 py-2 bg-blue-500 hover:bg-blue-600 disabled:bg-gray-400 text-white rounded-lg"
                            >
                                Import
                            </button>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
};

export default GraphExportImport;
